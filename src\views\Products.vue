<script setup lang="ts">
import { useRoute } from 'vue-router';
import PageLoader from '@/components/global/PageLoader.vue';
import { routeMap } from '@/router/routes';
import { initializeBasicBreadcrumbBehaviour } from '@/util/facades/breadcrumb';

const route = useRoute();

initializeBasicBreadcrumbBehaviour('products.title', routeMap.products.name, true, route);
</script>

<template>
  <router-view v-slot="{ Component }">
    <Suspense timeout="0">
      <template #default>
        <component :is="Component" />
      </template>
      <template #fallback>
        <transition appear :duration="200" mode="out-in">
          <PageLoader :absolute-center="true" />
        </transition>
      </template>
    </Suspense>
  </router-view>
</template>
