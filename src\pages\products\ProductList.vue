<script setup lang="ts">
import { IconCircleCheckFilled, IconCircleXFilled } from '@tabler/icons-vue';
import { useRouteQuery } from '@vueuse/router';
import debounce from 'lodash-es/debounce';
import { PlusIcon, Trash2, Edit, Search } from 'lucide-vue-next';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import PageLoader from '@/components/global/PageLoader.vue';
import Pagination from '@/components/global/Pagination.vue';
import { routeMap } from '@/router/routes';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/shadcn-components/ui/alert-dialog';
import { Button as ShadCnButton } from '@/shadcn-components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/shadcn-components/ui/table';
import { useAuthStore } from '@/stores/auth-store';
import adminApi from '@/util/adminAxios';
import { contrastTextColor } from '@/util/cash-register-utils';
import { formatDate } from '@/util/datetime';
import { getFromMultiLangObject } from '@/util/multilang';
import type { Category, Meta, Product, Response } from '@/util/types/api-responses';
import { ComponentStateType } from '@/util/types/components';

const { t } = useI18n();
const authStore = useAuthStore();
const currentPage = useRouteQuery('page');
const currentLimit = useRouteQuery('limit');

const componentState = ref(ComponentStateType.LOADING);
const products = ref<Product[]>([]);
const paginationMeta = ref<Meta>();
const productTypes = {
  product: t('products.product'),
  service: t('products.service'),
  deposit_packaging: t('products.deposit-packaging'),
  ticket: t('products.ticket'),
} as const;

const fetchData = async(args?: { page?: number, limit?: number, search?: string }) => {
  const to = setTimeout(() => {
    componentState.value = ComponentStateType.LOADING;
  }, 220);
  try {
    const params = {
      page: (args?.page ?? currentPage.value) ?? 1,
      limit: (args?.limit ?? currentLimit.value) ?? 15,
      include: 'categories',
    } as Record<string, string | number>;
    if (args?.search && args.search.length > 2) {
      params.search = args.search;
      delete params.page;
    }

    const { data } = await adminApi.get<Response<Product[]>>('/api/admin/products', { params });

    products.value = data.data;
    paginationMeta.value = data.meta;
    clearTimeout(to);
    componentState.value = ComponentStateType.OK;
  } catch {
    clearTimeout(to);
    componentState.value = ComponentStateType.ERROR;
  }
};

await fetchData();

const removeProduct = async(id: string) => {
  try {
    await adminApi.delete(`/api/admin/products/${id}`);
    await fetchData();
  } catch {
    componentState.value = ComponentStateType.ERROR;
  }
};

const onSearchInput = debounce((ev: Record<any, any>) => {
  fetchData({ search: ev.target?.value });
}, 500);
</script>

<template>
  <div class="flex flex-col">
    <div v-if="componentState === ComponentStateType.OK" class="flex flex-col">
      <div class="flex items-center gap-4 mb-2 flex-wrap">
        <router-link :class="{ 'pointer-events-none select-none opacity-50': !authStore.hasPermission('product manage') }" :to="{ name: routeMap.products.children.create.name }" class="w-fit bg-gray-200 rounded-lg p-2 hover:bg-gray-300 transition-colors gap-0.5 flex items-center cursor-pointer">
          <PlusIcon class="aspect-square text-gray-500" />
          <div class="text-sm font-medium text-gray-600">{{ $t('products.new-product') }}</div>
        </router-link>
        <div class="relative p-2 bg-white border-gray-300 border rounded-lg flex items-center w-64 overflow-hidden flex-1 sm:flex-none">
          <div class="pl-0.5 pr-1.5">
            <Search class="min-h-5 h-5 min-w-5 w-5 text-muted-foreground bg-mute" />
          </div>
          <input
            type="text"
            :placeholder="$t('misc.search')"
            class="w-full focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-gray-500 outline-none placeholder:text-sm"
            @input="onSearchInput"
          >
        </div>
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead class="text-center">#</TableHead>
            <TableHead>{{ $t('products.image') }}</TableHead>
            <TableHead>{{ $t('products.name') }}</TableHead>
            <TableHead>{{ $t('products.price') }}</TableHead>
            <TableHead>{{ $t('products.categories') }}</TableHead>
            <TableHead>{{ $t('misc.type') }}</TableHead>
            <TableHead>{{ $t('misc.created') }}</TableHead>
            <TableHead>{{ $t('products.active') }}</TableHead>
            <TableHead class="text-right">
              {{ $t('user-management.actions') }}
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-for="(product, idx) in products" :key="idx">
            <TableCell class="!py-0 w-6 text-center text-gray-500">
              <div>{{ idx + 1 }}.</div>
            </TableCell>

            <TableCell class="font-semibold !py-0 w-24">
              <img :src="product.cover_image_thumb" alt="" class="h-12" @click="$router.push({ name: routeMap.products.children.edit.name, params: { id: product.id } })">
            </TableCell>

            <TableCell v-if="product.name">
              <router-link :to="{ name: routeMap.products.children.edit.name, params: { id: product.id } }">
                <div class="hover:underline">{{ getFromMultiLangObject(product.name).value }}</div>
                <div class="fig gap-1">
                  <div class="text-xs bg-orange-500 font-medium text-white w-fit px-0.5 rounded">{{ $t('misc.code') }}: {{ product.code }}</div>
                  <div class="text-xs bg-emerald-500 font-medium text-white w-fit px-0.5 rounded">PLU: {{ product.plu }}</div>
                </div>
              </router-link>
            </TableCell>
            <TableCell v-else>-</TableCell>

            <TableCell v-if="product.price">
              <div class="">{{ product.price }}€</div>
            </TableCell>
            <TableCell v-else>-</TableCell>

            <TableCell v-if="product.categories?.length" class="flex-wrap space-x-2 space-y-2 space-x-reverse [&>div:first-child]:mr-2 max-w-[600px]">
              <div v-for="({ name, color: background }, i) in (product.categories as Category[])" :key="i" :style="{ background, color: contrastTextColor(background) }" class="inline-flex py-1 px-2 text-white rounded">{{ getFromMultiLangObject(name).value }}</div>
            </TableCell>
            <TableCell v-else>-</TableCell>

            <TableCell v-if="product.type">
              <div class="w-fit bg-paynes-gray-700 py-0.5 px-2 text-white rounded">{{ productTypes[product.type] }}</div>
            </TableCell>
            <TableCell v-else>-</TableCell>

            <TableCell v-if="product.created">
              <div>{{ formatDate(product.created) }}</div>
            </TableCell>
            <TableCell v-else>-</TableCell>

            <TableCell class="w-6">
              <IconCircleCheckFilled v-if="product.active" class="text-green-500 mx-auto" />
              <IconCircleXFilled v-else class="text-red-500 mx-auto" />
            </TableCell>

            <TableCell class="text-rights w-[110px] ml-auto flex gap-2 justify-end">
              <div class="inline-flex items-center">
                <router-link :to="{ name: routeMap.products.children.edit.name, params: { id: product.id } }" class="size-8 p-1.5 rounded-full bg-paynes-gray text-primary-foreground hover:bg-paynes-gray/90">
                  <Edit class="w-full h-full" />
                </router-link>
              </div>

              <AlertDialog>
                <AlertDialogTrigger :disabled="!authStore.hasPermission('product manage')" @click.prevent.stop>
                  <ShadCnButton class="w-8 h-8 p-1.5 rounded-full" variant="destructive">
                    <Trash2 class="w-full h-full" />
                  </ShadCnButton>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>{{ $t('form.delete-product', { name: getFromMultiLangObject(product.name).value }) }}</AlertDialogTitle>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>{{ $t('misc.cancel') }}</AlertDialogCancel>
                    <AlertDialogAction class="bg-destructive text-destructive-foreground hover:bg-destructive/90" @click="removeProduct(product.id)">{{ $t('misc.continue') }}</AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>

      <Pagination v-if="products?.length" :meta="paginationMeta!" @new-page="fetchData" />

      <div v-if="products?.length === 0" class="text-sm text-gray-400 w-full text-center mt-4">
        {{ $t('misc.list-is-empty') }}
      </div>
    </div>
    <div v-if="componentState === ComponentStateType.LOADING" class="absolute bg-white/70 top-0 left-0 w-full h-full rounded-3xl">
      <PageLoader :fixed-center="true" />
    </div>
    <div v-else-if="componentState === ComponentStateType.ERROR" class="absolute-center bg-black text-white font-bold p-1">
      <span>{{ $t('misc.failed-to-get-data') }}!</span>
    </div>
  </div>
</template>
