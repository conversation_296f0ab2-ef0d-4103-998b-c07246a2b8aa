<script setup lang="ts">
import { Save } from 'lucide-vue-next';
import { reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';
import { routeMap } from '@/router/routes';
import Button from '@/shadcn-components/ui/button/Button.vue';
import Input from '@/shadcn-components/ui/inputs/Input.vue';
import Select from '@/shadcn-components/ui/inputs/Select.vue';
import Label from '@/shadcn-components/ui/label/Label.vue';
import adminApi from '@/util/adminAxios';
import { deployToast, ToastType } from '@/util/toast';
import type { ACR, CR } from '@/util/types/api-responses';

const { t } = useI18n();
const router = useRouter();
const route = useRoute();

const associatedCRs = ref<ACR[]>([]);
const form = reactive<CR>({
  associated_cash_registers: [] as ACR[],
  product_image: true,
} as CR);

const addToAssociatedCRs = (items: CR[]) => {
  form.associated_cash_registers = items.map(item => {
    const existing = form.associated_cash_registers?.find(cr => cr.device_id === item.device_id);
    if (existing) {
      return existing;
    }

    return {
      id: item.id,
      device_id: item.device_id,
      name: item.name,
      frontend: false,
      printer: false,
      payment_terminal: false,
    };
  });
};

const submit = async() => {
  try {
    await adminApi.put(`/api/admin/cash-registers/${route.params.id}`, {
      ...form,
      cash_register_groups: form.cash_register_groups?.map(group => group.id),
    });
    deployToast(ToastType.SUCCESS, {
      text: t('settings.settings-saved'),
      timeout: 6000,
    });
    router.push({ name: routeMap.cr.children.list.name });
  } catch (e) {
    deployToast(ToastType.ERROR, {
      // @ts-ignore
      text: e.response?.data.message ?? t('misc.error'),
      timeout: 6000,
    });
  }
};

try {
  const [
    { data: crs },
    { data: formData },
  ] = await Promise.all([
    adminApi.get('/api/admin/cash-registers'),
    adminApi.get(`/api/admin/cash-registers/${route.params.id}`),
  ]);

  associatedCRs.value = crs.data;
  Object.assign(form, formData.data);
} catch (e) {
  deployToast(ToastType.ERROR, {
    text: t('misc.error'),
    timeout: 6000,
  });
}
</script>

<template>
  <form @submit.prevent>
    <h2 class="font-bold text-2xl text-paynes-gray-400">{{ $t('cash-register.settings') }}</h2>

    <div class="flex flex-col gap-4 mt-4">
      <div>
        <Label class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32">
          {{ $t('cash-register.associated-crs') }}
        </Label>

        <Select
          v-model="form.associated_cash_registers"
          :options="associatedCRs"
          multiple
          item-title="name"
          @selected="addToAssociatedCRs"
        />

        <div>
          <div v-for="item in form.associated_cash_registers" :key="item.device_id" class="border rounded-lg p-2 mt-2 bg-white">
            <div class="font-medium text-lg">
              {{ item.name ?? item.device_id }}
            </div>
            <div class="fig font-medium text-muted-foreground">
              <div>Frontends</div>
              <el-switch v-model="item.frontend" />
            </div>
            <div class="fig font-medium text-muted-foreground">
              <div>Printer</div>
              <el-switch v-model="item.printer" />
            </div>
            <div class="fig font-medium text-muted-foreground">
              <div>payment terminal</div>
              <el-switch v-model="item.payment_terminal" />
            </div>
          </div>
        </div>
      </div>

      <div class="max-w-sm">
        <div class="fig">
          <Label class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32">
            {{ $t('cash-register.printer') }}
          </Label>
          <el-switch
            v-model="form.printer"
          />
        </div>

        <div class="fig">
          <Label class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32">
            {{ $t('cash-register.payment-terminal') }}
          </Label>
          <el-switch
            v-model="form.payment_terminal"
          />
        </div>

        <div class="fig">
          <Label class="text-right whitespace-nowrap">
            {{ $t('cash-register.auto-logout-label') }}
          </Label>
          <Input
            v-model="form.auto_logout"
            type="number"
            min="1"
            fit
            class="!w-16"
          />
          <span class="w-fit">{{ $t('cash-register.minutes', { count: form.auto_logout }) }}</span>
        </div>

        <div class="fig">
          <Label class="text-right whitespace-nowrap">
            {{ $t('cash-register.product-images') }}
          </Label>
          <el-switch
            v-model="form.product_image"
          />
        </div>
      </div>

    </div>
    <div class="flex justify-end mt-8">
      <Button class="w-fit space-x-2 bg-green-600 hover:bg-green-700 ml-auto" @click.prevent="submit">
        <Save stroke-width="1.5" />
        <span>{{ $t('misc.save') }}</span>
      </Button>
    </div>
  </form>
</template>
