<script setup lang="ts">
import { ref, watch } from 'vue';

const props = defineProps<{
  modelValue?: number | null;
  disabled?: boolean;
  errors?: string[];
}>();

const emits = defineEmits<{(e: 'update:modelValue', value: number | null): void}>();

const days = ref(0);
const hours = ref(0);
const minutes = ref(0);

const convertSecondsToTime = (seconds: number | null) => {
  if (!seconds) {
    days.value = 0;
    hours.value = 0;
    minutes.value = 0;
    return;
  }

  const totalMinutes = Math.floor(seconds / 60);
  const totalHours = Math.floor(totalMinutes / 60);

  days.value = Math.floor(totalHours / 24);
  hours.value = totalHours % 24;
  minutes.value = totalMinutes % 60;
};

const convertTimeToSeconds = () => {
  const totalSeconds = (days.value * 24 * 60 * 60) + (hours.value * 60 * 60) + (minutes.value * 60);
  return totalSeconds;
};

watch(() => props.modelValue, (newValue) => {
  if (!newValue || !Number.isInteger(newValue)) {
    return;
  }
  convertSecondsToTime(newValue);
}, { immediate: true });

watch([days, hours, minutes], () => {
  const totalSeconds = convertTimeToSeconds();
  emits('update:modelValue', totalSeconds);
});

const handleDaysInput = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const value = parseInt(target.value) || 0;
  days.value = Math.max(0, value);
};

const handleHoursInput = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const value = parseInt(target.value) || 0;
  hours.value = Math.max(0, Math.min(23, value));
};

const handleMinutesInput = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const value = parseInt(target.value) || 0;
  minutes.value = Math.max(0, Math.min(59, value));
};
</script>

<template>
  <div class="w-full">
    <div class="flex items-center gap-2">
      <!-- Days -->
      <div class="flex flex-col items-center">
        <input
          :value="days"
          type="number"
          min="0"
          :disabled="disabled"
          class="w-12 text-center border rounded px-1 py-1 text-sm disabled:opacity-50 disabled:cursor-not-allowed focus:outline-paynes-gray-900"
          :class="{ 'focus:outline-rose-500 border-rose-500 bg-rose-50/50': errors?.length }"
          @input="handleDaysInput"
        >
        <span class="text-xs text-gray-500 mt-0.5">{{ $t('misc.plural-handled-days', days) }}</span>
      </div>

      <span class="text-gray-400">:</span>

      <!-- Hours -->
      <div class="flex flex-col items-center">
        <input
          :value="hours.toString().padStart(2, '0')"
          type="number"
          min="0"
          max="23"
          :disabled="disabled"
          class="w-12 text-center border rounded px-1 py-1 text-sm disabled:opacity-50 disabled:cursor-not-allowed focus:outline-paynes-gray-900"
          :class="{ 'focus:outline-rose-500 border-rose-500 bg-rose-50/50': errors?.length }"
          @input="handleHoursInput"
        >
        <span class="text-xs text-gray-500 mt-0.5">{{ $t('misc.plural-handled-hours', hours) }}</span>
      </div>

      <span class="text-gray-400">:</span>

      <!-- Minutes -->
      <div class="flex flex-col items-center">
        <input
          :value="minutes.toString().padStart(2, '0')"
          type="number"
          min="0"
          max="59"
          :disabled="disabled"
          class="w-12 text-center border rounded px-1 py-1 text-sm disabled:opacity-50 disabled:cursor-not-allowed focus:outline-paynes-gray-900"
          :class="{ 'focus:outline-rose-500 border-rose-500 bg-rose-50/50': errors?.length }"
          @input="handleMinutesInput"
        >
        <span class="text-xs text-gray-500 mt-0.5">{{ $t('misc.plural-handled-minutes', minutes) }}</span>
      </div>
    </div>

    <!-- Error messages -->
    <div v-for="error in errors" :key="error" class="text-rose-500 text-xs mt-0.5 ml-1.5">
      {{ error }}
    </div>
  </div>
</template>
