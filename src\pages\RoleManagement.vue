<script lang="ts" setup>
import { PlusIcon } from 'lucide-vue-next';
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import PageLoader from '@/components/global/PageLoader.vue';
import RolesTable from '@/pages/role-management/RolesTable.vue';
import { routeMap } from '@/router/routes';
import adminApi from '@/util/adminAxios';
import { initializeBasicBreadcrumbBehaviour } from '@/util/facades/breadcrumb';
import { cloneObject } from '@/util/objects';
import type { ApiPermissionsResponse, ApiRolesResponse } from '@/util/types/api-responses';
import { ComponentStateType } from '@/util/types/components';
import type { PermissionData, RoleData } from '@/util/types/roles-and-permissions';

const route = useRoute();
initializeBasicBreadcrumbBehaviour(routeMap.management.children.roles.meta.i18nTitle, routeMap.management.children.roles.name, true, route);

const componentState = ref(ComponentStateType.LOADING);
const formerRolesData = ref<RoleData[]>([]);
const roleDataChanges = ref<RoleData[]>([]);
const allPermissions = ref<PermissionData[]>([]);
const creatingNewItem = ref(false);

const reFetchData = async() => {
  componentState.value = ComponentStateType.LOADING;
  try {
    const usersResponse = await adminApi.get<ApiRolesResponse>('/api/admin/roles');
    formerRolesData.value = cloneObject(usersResponse.data.data);
    roleDataChanges.value = usersResponse.data.data;
    const { data: permissionsResponse } = await adminApi.get<ApiPermissionsResponse>('/api/admin/permissions');
    allPermissions.value = permissionsResponse.data;
    componentState.value = ComponentStateType.OK;
  } catch {
    componentState.value = ComponentStateType.ERROR;
  }
};

const setCreatingNew = (val: boolean) => {
  creatingNewItem.value = val;
};

await reFetchData();

const onNewRoleCreated = () => {
  creatingNewItem.value = false;
  reFetchData();
};

</script>

<template>
  <div class="relative">
    <div v-if="componentState !== ComponentStateType.ERROR" class="flex items-center justify-between">
      <div class="w-fit bg-gray-200 rounded-lg p-2 hover:bg-gray-300 transition-colors flex items-center cursor-pointer" @click="creatingNewItem = true;">
        <PlusIcon class="aspect-square text-gray-500" />
      </div>
    </div>
    <RolesTable
      v-if="roleDataChanges.length > 0"
      v-model="roleDataChanges"
      :former-role-data="formerRolesData"
      :permissions="allPermissions"
      :creating-new="creatingNewItem"
      @set-creating-new="setCreatingNew"
      @new-role-created="onNewRoleCreated"
      @role-deleted="reFetchData"
      @role-updated="reFetchData"
    />
    <transition appear>
      <div v-if="componentState === ComponentStateType.LOADING" class="absolute left-0 top-0 w-full h-full bg-white/70 flex items-center justify-center">
        <PageLoader :flex-center="true" />
      </div>
    </transition>
    <div v-if="componentState === ComponentStateType.ERROR" class="absolute left-0 top-0 w-full min-h-[10rem] bg-red-200 h-full bg-white/70 flex items-center justify-center">
      <span class="font-medium">{{ $t('misc.failed-to-get-data') }}</span>
    </div>
  </div>
</template>
