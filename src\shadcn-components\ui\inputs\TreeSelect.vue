<script setup lang="ts">
import { useVModel } from '@vueuse/core';
import { useI18n } from 'vue-i18n';

const { locale } = useI18n();

const props = defineProps<{
  defaultValue?: any
  modelValue?: any
  disabled?: boolean
  errors?: string[]
  options?: any
  multiple?: boolean
  placeholder?: string
  normalizer?:(node: any) => any
}>();

const emits = defineEmits<{(e: 'update:modelValue', payload: string | number): void}>();

const modelValue = useVModel(props, 'modelValue', emits, {
  passive: true,
  defaultValue: props.defaultValue,
});

function transformLabels(data: any[]): any[] {
  return data.map(item => {
    const transformedItem: any = {
      ...item,
      label: item.label?.[locale.value] || '',
    };

    if (item.children && Array.isArray(item.children)) {
      transformedItem.children = transformLabels(item.children);
    }

    return transformedItem;
  });
}

const options = transformLabels(props.options);
</script>

<template>
  <div class="w-full relative">
    <el-tree-select
      v-model="modelValue"
      :data="options"
      :multiple
      node-key="id"
      filterable
      :placeholder="placeholder ?? ''"
      show-checkbox
      check-strictly
    />
    <div class="top-10">
      <div v-for="error in errors" :key="error" class="text-rose-500 text-xs mt-0.5 ml-1.5">{{ error }}</div>
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-select__wrapper) {
  height: 40px !important;
  padding: 8px 14px !important;
  border-radius: 0.375rem !important;
  border: 1px solid hsl(var(--input)) !important;
  box-shadow:  rgba(0, 0, 0, 0) 0 0 0 0, rgba(0, 0, 0, 0) 0 0 0 0, rgba(0, 0, 0, 0.1) 0 1px 3px 0, rgba(0, 0, 0, 0.1) 0 1px 2px -1px !important;
  transition: none !important;
  &:focus-within {
    outline: 2px solid #d2e2e7 !important;
    outline-offset: -3px;
    border: 1px solid transparent !important;
    border-radius: 0.475rem !important;
  }
}

:deep(.vue-treeselect__control) {
  min-height: 40px !important;
  border-color: hsl(var(--border));
  border-radius: 0.375rem !important;
  fill: #686d73 !important;
  @apply shadow;
}

:deep(.vue-treeselect__control-arrow) {
  fill: #686d73 !important;
}

:deep(.error-tree-select .vue-treeselect__control) {
  border-color: theme('colors.rose.500');
  background: theme('colors.rose.50/50');
}

:deep(.vue-treeselect__placeholder) {
  line-height: 38px;
}
:deep(.vue-treeselect__input) {
  font-size: 0.9rem;
}
:deep(.vue-treeselect__multi-value-item-container) {
  padding-top: 7px;
}
</style>
