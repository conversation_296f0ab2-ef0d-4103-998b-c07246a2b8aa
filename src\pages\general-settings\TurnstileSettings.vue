<script setup lang="ts">
import { Check, Edit, X } from 'lucide-vue-next';
import { computed, onMounted, reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import PageLoader from '@/components/global/PageLoader.vue';
import { Button } from '@/shadcn-components/ui/button';
import Input from '@/shadcn-components/ui/inputs/Input.vue';
import Select from '@/shadcn-components/ui/inputs/Select.vue';
import Label from '@/shadcn-components/ui/label/Label.vue';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/shadcn-components/ui/table';
import adminApi from '@/util/adminAxios';
import { deployToast, ToastType } from '@/util/toast';
import type { SystemMode } from '@/util/types/api-responses';

const { t } = useI18n();

const timezones = ref(['Europe/Bratislava']);
const timezone = ref(timezones.value[0]);
const form = reactive<SystemMode>({
  mode: 'OPENING_HOURS',
  turnstile_display_text: '',
  opening_hours: [],
});
let ogForm: SystemMode;
const loading = ref(false);

const editingId = ref();

const getSystemMode = async() => {
  const to = setTimeout(() => {
    loading.value = true;
  }, 220);
  const { data: { data }} = await adminApi.get('/api/admin/system-mode');
  clearTimeout(to);
  loading.value = false;
  Object.assign(form, data);
  ogForm = JSON.parse(JSON.stringify(data));
};

const saveEdits = async() => {
  console.log('Saving edits', form);
  try {
    await adminApi.put('/api/admin/system-mode', {
      mode: form.mode,
      turnstile_display_text: form.turnstile_display_text,
      opening_hours: form.opening_hours.map(day => ({
        ...day,
        timezone: timezone.value,
      })),
    });
    deployToast(ToastType.SUCCESS, {
      text: t('settings.settings-saved'),
      timeout: 6000,
    });
    editingId.value = undefined;
  } catch {
    deployToast(ToastType.ERROR, {
      text: t('misc.error'),
      timeout: 6000,
    });
  }
};

const cancelEdits = () => {
  form.opening_hours = ogForm.opening_hours;
  console.log('Canceling edits', ogForm.opening_hours, form);
  editingId.value = undefined;
};

const swappedDays = computed(() => ['mo', 'tu', 'we', 'th', 'fr', 'sa', 'su'].map(shortcut => form.opening_hours.find(day => day.shortcut === shortcut)).filter(Boolean)) as unknown as SystemMode['opening_hours'];

onMounted(getSystemMode);
</script>

<template>
  <div v-if="loading" class="w-full md:min-w-[500px]">
    <PageLoader absolute-center />
  </div>
  <div v-else class="p-4 relative w-full md:min-w-[500px] space-y-2">
    <div>
      <Label>{{ $t('settings.turnstile-display-text') }}</Label>
      <Input
        v-model="form.turnstile_display_text"
        class="w-full mb-4"
      />
    </div>

    <div class="grid grid-cols-2">
      <div class="grid gap-1">
        <Label>{{ $t('settings.turnstile-mode') }}</Label>
        <el-radio-group v-model="form.mode" fill="#769BAE">
          <el-radio-button :label="$t('settings.openingHours')" value="OPENING_HOURS" />
          <el-radio-button :label="$t('settings.event')" value="EVENT" />
        </el-radio-group>
      </div>

      <div class="grid gap-1">
        <Label>{{ $t('settings.turnstile-timezone') }}</Label>
        <Select
          v-model="timezone"
          :options="timezones"
        />
      </div>
    </div>

    <div class="!-mb-2">
      <Label>{{ $t('settings.openingHours') }}</Label>
    </div>
    <Table class="users-table">
      <TableHeader>
        <TableRow>
          <TableHead>
            {{ $t('misc.day') }}
          </TableHead>
          <TableHead>
            {{ $t('misc.start') }}
          </TableHead>
          <TableHead>
            {{ $t('misc.end') }}
          </TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        <TableRow v-for="(day, idx) in swappedDays" :key="day.shortcut" :class="[editingId === day.shortcut ? 'bg-yellow-100 hover:bg-yellow-100' : (idx % 2 === 0 ? 'bg-accent' : '')]">
          <TableCell>
            {{ $t(`misc.days.${day.shortcut}`) }}
          </TableCell>
          <TableCell>
            <Input
              v-if="editingId === day.shortcut"
              v-model="day.start"
              type="time"
              required
              class="ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0 focus-visible:border-gray-500 min-w-[7rem] max-w-[240px]"
            />
            <div v-else>{{ day.start }}</div>
          </TableCell>
          <TableCell>
            <Input
              v-if="editingId === day.shortcut"
              v-model="day.end"
              type="time"
              required
              class="ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0 focus-visible:border-gray-500 min-w-[7rem] max-w-[240px]"
            />
            <div v-else>{{ day.end }}</div>
          </TableCell>
          <TableCell class="text-right w-36">
            <div v-if="editingId === day.shortcut" class="inline-flex items-center gap-1">
              <Button class="size-8 p-1.5 rounded-full bg-green-500 hover:bg-green-600" @click="saveEdits">
                <Check class="w-full h-full" />
              </Button>
              <Button class="size-8 p-1.5 rounded-full " @click="cancelEdits">
                <X class="w-full h-full" />
              </Button>
            </div>
            <Button v-else class="size-8 p-1.5 rounded-full" @click="editingId = day.shortcut">
              <Edit class="w-full h-full" />
            </Button>
          </TableCell>
        </TableRow>
      </TableBody>
    </Table>

    <div class="flex !mt-4">
      <Button class="ml-auto bg-green-600 hover:bg-green-700" @click="saveEdits">
        {{ $t('misc.save') }}
      </Button>
    </div>
  </div>
</template>
