import { mergeDeep } from '@/util/objects';
import { Layouts } from '@/util/types/layouts';
import type { RouteRecordRaw } from 'vue-router';

export type ARMRouteMap = {
  [key: string]: Pick<RouteRecordRaw, 'path' | 'name'> & {meta?: {i18nTitle?: string, layout?: () => Layouts, authNotRequired?: boolean, requiresPermission?: string }, children?: ARMRouteMap};
}

export const routeMap = {
  home: {
    path: '/',
    name: 'home',
    meta: {
      i18nTitle: 'misc.dashboard',
      layout: () => Layouts.BASIC_WITH_MENU,
    },
  },
  cashRegister: {
    path: '/cash-register',
    name: 'cash-register',
    meta: {
      layout: () => Layouts.EMPTY,
      requiresPermission: 'cash register',
    },
  },
  cr: {
    path: '/cr',
    name: 'cr',
    meta: {
      layout: () => Layouts.BASIC_WITH_MENU,
      i18nTitle: 'cash-register.cash-registers',
      requiresPermission: 'cash register admin',
    },
    children: {
      list: {
        path: '',
        name: 'cr.list',
        meta: {
          i18nTitle: 'misc.list',
        },
      },
      groups: {
        path: 'groups',
        name: 'cr.groups',
        meta: {
          i18nTitle: 'cash-register.groups-of-cash-registers',
        },
      },
      editor: {
        path: 'editor/:id',
        name: 'cr.editor',
      },
      settings: {
        path: 'settings/:id',
        name: 'cr.settings',
      },
    },
  },
  products: {
    path: '/products',
    name: 'products',
    meta: {
      layout: () => Layouts.BASIC_WITH_MENU,
      i18nTitle: 'products.title',
      requiresPermission: 'product view',
    },
    children: {
      list: {
        path: '',
        name: 'products.list',
        meta: {
          i18nTitle: 'misc.list',
        },
      },
      categories: {
        path: 'categories',
        name: 'products.categories',
        meta: {
          i18nTitle: 'products.categories',
        },
      },
      families: {
        path: 'families',
        name: 'products.families',
        meta: {
          i18nTitle: 'products.families',
        },
      },
      familyCreate: {
        path: 'family-create',
        name: 'products.family-create',
        meta: {
          i18nTitle: 'products.families',
        },
      },
      familyEdit: {
        path: 'family-edit/:id',
        name: 'products.family-edit',
        meta: {
          i18nTitle: 'products.families',
        },
      },
      create: {
        path: 'create',
        name: 'products.create',
        meta: {
          i18nTitle: 'products.title',
        },
      },
      edit: {
        path: 'edit/:id',
        name: 'products.edit',
        meta: {
          i18nTitle: 'products.title',
        },
      },
    },
  },
  orders: {
    path: '/orders',
    name: 'orders',
    meta: {
      layout: () => Layouts.BASIC_WITH_MENU,
      i18nTitle: 'orders.nav-title',
      requiresPermission: 'order view',
    },
    children: {
      list: {
        path: '',
        name: 'orders.list',
        meta: {
          i18nTitle: 'orders.title',
        },
      },
      create: {
        path: 'create',
        name: 'orders.create',
        meta: {
          i18nTitle: 'orders.list',
        },
      },
      edit: {
        path: 'edit/:id',
        name: 'orders.edit',
        meta: {
          i18nTitle: 'orders.list',
        },
      },
    },
  },
  turnstiles: {
    path: '/turnstiles',
    name: 'turnstiles',
    meta: {
      layout: () => Layouts.BASIC_WITH_MENU,
      i18nTitle: 'turnstiles.title',
      requiresPermission: 'turnstile view',
    },
    children: {
      list: {
        path: '',
        name: 'turnstiles.list',
        meta: {
          i18nTitle: 'misc.list',
        },
      },
      categories: {
        path: 'categories',
        name: 'turnstiles.categories',
        meta: {
          i18nTitle: 'turnstiles.categories',
        },
      },
    },
  },
  customers: {
    path: '/customers',
    name: 'customers',
    meta: {
      layout: () => Layouts.BASIC_WITH_MENU,
      i18nTitle: 'customers.title',
      requiresPermission: 'customer view',
    },
    children: {
      list: {
        path: '',
        name: 'customers.list',
        meta: {
          i18nTitle: 'misc.list',
        },
      },
      groups: {
        path: 'groups',
        name: 'customers.groups',
        meta: {
          i18nTitle: 'customers.groups',
        },
      },
      create: {
        path: 'create',
        name: 'customers.customer-create',
        meta: {
          i18nTitle: 'customers.groups',
        },
      },
      edit: {
        path: 'edit/:id',
        name: 'customers.customer-edit',
        meta: {
          i18nTitle: 'customers.groups',
        },
      },
    },
  },

  resetPassword: {
    path: '/password/reset/:token',
    name: 'user.reset-password',
    meta: {
      layout: () => Layouts.EMPTY,
      i18nTitle: 'login.reset-password',
      authNotRequired: true,
    },
  },
  forgotPassword: {
    path: '/forgot-password',
    name: 'user.forgot-password',
    meta: {
      layout: () => Layouts.BASIC_WITH_MENU,
      i18nTitle: 'login.forgotten-password',
      authNotRequired: true,
    },
  },
  management: {
    path: '/manage',
    name: 'manage',
    children: {
      users: {
        path: 'users',
        name: 'manage.users',
        meta: {
          i18nTitle: 'user-management.title',
          requiresPermission: 'manage user',

        },
      },
      roles: {
        path: 'roles',
        name: 'manage.roles',
        meta: {
          i18nTitle: 'role-management.title',
          requiresPermission: 'manage role',

        },
      },
    },
    meta: {
      layout: () => Layouts.BASIC_WITH_MENU,
      i18nTitle: '',
    },
  },
  settings: {
    path: '/settings',
    name: 'settings',
    meta: {
      layout: () => Layouts.BASIC_WITH_MENU,
      i18nTitle: 'misc.settings',
      requiresPermission: 'manage settings',
    },
    children: {
      changePassword: {
        path: 'password',
        name: 'settings.password',
        meta: {
          i18nTitle: 'settings.change-password',
        },
      },
      turnstileSettings: {
        path: 'turnstiles',
        name: 'settings.turnstiles',
        meta: {
          i18nTitle: 'turnstiles.title',
        },
      },
      unitsSettings: {
        path: 'units',
        name: 'settings.units',
        meta: {
          i18nTitle: 'settings.measurement-units',
        },
      },
      vatSettings: {
        path: 'vat',
        name: 'settings.vat',
        meta: {
          i18nTitle: 'settings.taxesTitle',
        },
      },
    },
  },
} as const satisfies ARMRouteMap;

export const routeIndex = {
  home: {
    ...routeMap.home,
    component: () => import('@/views/HomePage.vue'),
  },
  cashRegister: {
    ...routeMap.cashRegister,
    component: () => import('@/views/CashRegister.vue'),
  },
  cr: {
    ...routeMap.cr,
    component: () => import('@/views/CR.vue'),
    children: [
      {
        ...routeMap.cr.children.list,
        component: () => import('@/pages/cr/CRList.vue'),
      },
      {
        ...routeMap.cr.children.editor,
        component: () => import('@/pages/cr/CREditor.vue'),
      },
      {
        ...routeMap.cr.children.settings,
        component: () => import('@/pages/cr/CRSettings.vue'),
      },
      {
        ...routeMap.cr.children.groups,
        component: () => import('@/pages/cr/CRGroups.vue'),
      },
    ],
  },
  products: {
    ...routeMap.products,
    component: () => import('@/views/Products.vue'),
    children: [
      {
        ...routeMap.products.children.list,
        component: () => import('@/pages/products/ProductList.vue'),
      },
      {
        ...routeMap.products.children.categories,
        component: () => import('@/pages/products/ProductCategories.vue'),
      },
      {
        ...routeMap.products.children.families,
        component: () => import('@/pages/products/ProductFamilies.vue'),
      },
      {
        ...routeMap.products.children.familyCreate,
        component: () => import('@/pages/products/ProductFamilyForm.vue'),
      },
      {
        ...routeMap.products.children.familyEdit,
        component: () => import('@/pages/products/ProductFamilyForm.vue'),
      },
      {
        ...routeMap.products.children.create,
        component: () => import('@/pages/products/ProductForm.vue'),
      },
      {
        ...routeMap.products.children.edit,
        component: () => import('@/pages/products/ProductForm.vue'),
      },
    ],
  },
  turnikety: {
    ...routeMap.turnstiles,
    component: () => import('@/views/Turnstiles.vue'),
    children: [
      {
        ...routeMap.turnstiles.children.list,
        component: () => import('@/pages/turnstiles/TurnstilesList.vue'),
      },
      {
        ...routeMap.turnstiles.children.categories,
        component: () => import('@/pages/turnstiles/TurnstilesCategories.vue'),
      },
    ],
  },
  customers: {
    ...routeMap.customers,
    component: () => import('@/views/Customers.vue'),
    children: [
      {
        ...routeMap.customers.children.list,
        component: () => import('@/pages/customers/CustomerList.vue'),
      },
      {
        ...routeMap.customers.children.groups,
        component: () => import('@/pages/customers/CustomerGroupsList.vue'),
      },
      {
        ...routeMap.customers.children.create,
        component: () => import('@/pages/customers/CustomerForm.vue'),
      },
      {
        ...routeMap.customers.children.edit,
        component: () => import('@/pages/customers/CustomerForm.vue'),
      },
    ],
  },
  resetPassword: {
    ...routeMap.resetPassword,
    component: () => import('@/views/ResetPassword.vue'),
  },
  forgotPassword: {
    ...routeMap.forgotPassword,
    component: () => import('@/views/ForgotPassword.vue'),
  },
  management: {
    ...routeMap.management,
    component: () => import('@/views/Management.vue'),
    redirect: `/manage/${routeMap.management.children.users.path}`,
    children: [
      {
        ...routeMap.management.children.users,
        component: () => import('@/pages/UserManagement.vue'),
      },
      {
        ...routeMap.management.children.roles,
        component: () => import('@/pages/RoleManagement.vue'),
      },
    ],
  },
  settings: {
    ...routeMap.settings,
    component: () => import('@/views/Settings.vue'),
    children: [
      {
        ...routeMap.settings.children.changePassword,
        component: () => import('@/pages/ChangePassword.vue'),
      },
      {
        ...routeMap.settings.children.turnstileSettings,
        component: () => import('@/pages/general-settings/TurnstileSettings.vue'),
      },
      {
        ...routeMap.settings.children.unitsSettings,
        component: () => import('@/pages/general-settings/UnitsSettings.vue'),
      },
      {
        ...routeMap.settings.children.vatSettings,
        component: () => import('@/pages/general-settings/TaxSettings.vue'),
      },
    ],
  },
};

const routesArray = Object.values(mergeDeep({}, routeIndex)).map(elm => {
  // @ts-ignore
  if (elm.children) {
    // @ts-ignore
    elm.children = Object.values(elm.children);
  }
  return elm;
});

const mapFilter = (route: RouteRecordRaw) => {
  if (route.meta?.enabled === false) {
    return false;
  }
  if (!route.children) {
    return true;
  }
  route.children = route.children.filter(mapFilter);
  return true;
};

// @ts-ignore
export const routes: Array<RouteRecordRaw> = routesArray.filter(mapFilter);
