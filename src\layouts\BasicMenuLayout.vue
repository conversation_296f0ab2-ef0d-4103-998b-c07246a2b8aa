<script lang="ts" setup>
import { ref } from 'vue';
import VueEasyLightbox from 'vue-easy-lightbox';
import 'vue-easy-lightbox/external-css/vue-easy-lightbox.css';
import NavBar from '@/components/global/NavBar.vue';
import SideBar from '@/components/global/SideBar.vue';
import { SidebarProvider } from '@/shadcn-components/ui/sidebar';
import { TooltipProvider } from '@/shadcn-components/ui/tooltip';
import { useAuthStore } from '@/stores/auth-store';
import { useLightBoxStore } from '@/stores/light-box-store';

const lightBoxStore = useLightBoxStore();
const authStore = useAuthStore();

const sidebarState = ref(localStorage.getItem('sidebarState') ? localStorage.getItem('sidebarState') === 'true' : false);
const closeSidebar = () => {
  if (localStorage.getItem('sidebarState') === 'true') {
    return;
  }
  sidebarState.value = false;
};
</script>

<template>
  <template v-if="authStore.user">
    <transition appear :duration="200">
      <div class="min-h-[100dvh] flex w-full flex-col bg-muted/40">
        <SidebarProvider v-model:open="sidebarState">
          <TooltipProvider>
            <SideBar @open-sidebar="sidebarState = true" @close-sidebar="closeSidebar" />
          </TooltipProvider>

          <div class="flex flex-col sm:gap-4 pb-4 w-full">
            <nav-bar />
            <main class="flex-1 p-4 sm:px-6 sm:py-0 ">
              <slot />
            </main>
          </div>

          <VueEasyLightbox
            :visible="lightBoxStore.visible"
            :imgs="lightBoxStore.images"
            :index="lightBoxStore.index"
            @hide="lightBoxStore.visible = false"
          />
        </SidebarProvider>
      </div>
    </transition>
  </template>
  <template v-else>
    <slot />
  </template>
</template>
