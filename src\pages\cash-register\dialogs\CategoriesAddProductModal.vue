<script setup lang="ts">
import { IconMinus, IconPlus } from '@tabler/icons-vue';
import { ref } from 'vue';
import { useCashRegisterStore } from '@/stores/cash-register';
import cashApi from '@/util/cashAxios';
import { getFromMultiLangObject } from '@/util/multilang';
import type { Product } from '@/util/types/api-responses';

const store = useCashRegisterStore();

const emit = defineEmits(['close']);
const props = defineProps<{ product: Product }>();

const quantity = ref(1);

const submit = async() => {
  try {
    if (!store.order) {
      const { data: { data }} = await cashApi.post('/api/cash-register/orders');
      store.order = data;
    }

    const { data: { data }} = await cashApi.post(`/api/cash-register/orders/${store.order!.id}/items`, {
      items: [{
        product_id: props.product.id,
        quantity: quantity.value,
      }],
    });

    store.order = data;
  } catch (error) {
    // htmlLog(error);
  } finally {
    emit('close');
  }
};
</script>

<template>
  <div class="fixed inset-0 z-50 bg-black/65 grid place-items-center font-medium" @click.self="emit('close')">
    <div class="max-w-[380px] bg-white p-6 space-y-6 rounded-xl">
      <h2 class="text-2xl font-bold">{{ getFromMultiLangObject(product?.name) }}</h2>

      <div>
        <div class="text-lg text-gray-500">{{ $t('products.quantity') }}:</div>
        <div class="w-fit rounded-md mx-auto flex flex-nowrap">
          <button :class="[quantity === 1 && 'opacity-40 active:bg-gray-50', 'relative border rounded-l-md text-center py-2 w-28 bg-gray-50 active:bg-gray-100 text-transparent border-r-0 focus:outline-none']" @click="quantity > 1 && quantity--">
            <IconMinus stroke-width="2.5" class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-gray-500" />
            -
          </button>
          <input v-model="quantity" type="number" class="border text-2xl text-center py-4 w-full focus:outline-none">
          <button class="relative border rounded-r-md text-center w-28 bg-gray-50 active:bg-gray-100 text-transparent border-l-0 focus:outline-none" @click="quantity++">
            +
            <IconPlus stroke-width="2.5" class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-gray-500" />
          </button>
        </div>
      </div>

      <div class="flex justify-end">
        <button class="bg-green-600 active:bg-green-700 text-white py-3.5 px-5 rounded-md text-lg" @click="submit">{{ $t('misc.add') }}</button>
      </div>
    </div>
  </div>
</template>

<style scoped>
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
</style>
