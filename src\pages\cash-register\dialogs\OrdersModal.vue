<script setup lang="ts">
import { ref } from 'vue';
import Pagination from '@/components/global/Pagination.vue';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/shadcn-components/ui/table';
import { useCashRegisterStore } from '@/stores/cash-register';
import cashApi from '@/util/cashAxios';
import { formatDate } from '@/util/datetime';
import type { CROrder, Response } from '@/util/types/api-responses';

const emit = defineEmits(['close']);

const store = useCashRegisterStore();

const orders = ref<Response<CROrder[]>>();

const fetchData = async({ page = 1 }: { page?: number } = {}) => {
  const { data } = await cashApi.get<Response<CROrder[]>>('api/cash-register/orders', {
    params: {
      page,
      limit: 10,
      'filter[state]': 'PENDING',
    },
  });

  orders.value = data;
};

const loadOrder = (id: string) => {
  store.loadCart(id);
  emit('close');
};

const cancelOrder = async(id: string) => {
  await cashApi.put(`/api/cash-register/orders/${id}`, {
    state: 'CANCELLED',
  });
  fetchData();
};

fetchData();
</script>

<template>
  <div class="fixed inset-0 z-50 bg-black/65 grid place-items-center" @click.self="emit('close')">
    <div class="max-w-[600px] w-full bg-white p-10 rounded-xl">
      <div class="space-y-4">

        <h2 class="text-xl font-bold">{{ $t('cash-register.unpaid-checks') }}</h2>

        <Table v-if="orders?.data.length" class="mt-4 text-base">
          <TableHeader>
            <TableRow>
              <TableHead>#</TableHead>
              <TableHead>{{ $t('cash-register.items') }}</TableHead>
              <TableHead>{{ $t('cash-register.total') }}</TableHead>
              <TableHead>{{ $t('misc.date') }}</TableHead>
              <TableHead />
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow v-for="(order, idx) in orders.data" :key="order.id" @click="loadOrder(order.id)">
              <TableCell>{{ idx + 1 }}.</TableCell>

              <TableCell v-if="order.items?.length">
                <div class="border rounded-full w-fit px-3 bg-emerald-500 text-white">{{ $t('cash-register.n-items', { n: order.items?.length ?? 0 }) }}</div>
              </TableCell>
              <TableCell v-else>
                <div class="border rounded-full w-fit px-3 bg-gray-400 text-white">{{ $t('cash-register.n-items', { n: order.items?.length ?? 0 }) }}</div>
              </TableCell>

              <TableCell v-if="order.items?.length" :class="[order.price_calculated !== order.price && 'text-emerald-500', 'font-medium']">{{ order.price_calculated.toFixed(2) }} €</TableCell>
              <TableCell v-else class="font-medium">0.00 €</TableCell>

              <TableCell class=" text-gray-400">{{ formatDate(order.created_at, true) }}</TableCell>
              <TableCell>
                <button class="border rounded py-0.5 px-1.5 shadow" @click.stop="cancelOrder(order.id)">{{ $t('misc.cancel') }}</button>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
        <div v-else class="text-center text-gray-400 italic py-4">No orders found</div>

        <Pagination v-if="orders?.meta" in-cash-register :meta="orders.meta" @new-page="fetchData" />
      </div>
    </div>
  </div>
</template>
