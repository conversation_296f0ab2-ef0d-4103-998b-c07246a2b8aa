<script setup lang="ts">
import { IconFileTypePdf, IconFileTypeXls, IconFileText, IconX } from '@tabler/icons-vue';
import { computed } from 'vue';

const emit = defineEmits(['removeItem', 'openLightBox']);
const props = defineProps<{
  name?: string
  thumb?: string
  url?: string
  mimeType?: string
  readOnly?: boolean,
}>();

const getMimeIcon = computed(() => {
  if (props.mimeType?.includes('pdf')) {
    return IconFileTypePdf;
  } else if (props.mimeType?.includes('excel') || props.mimeType?.includes('spreadsheet')) {
    return IconFileTypeXls;
  } else {
    return IconFileText;
  }
});
</script>

<template>
  <div class="w-24 h-28">
    <div class="group relative flex">
      <img v-if="mimeType?.includes('image')" :src="thumb" class="size-14 mx-auto rounded object-cover" alt="product attachment thumb" @click="emit('openLightBox')">
      <a v-else :href="url" target="_blank" rel="noopener noreferrer" class="flex-1"><component :is="getMimeIcon" class="size-12 text-primary mx-auto" /></a>

      <button v-if="!readOnly" @click.prevent="emit('removeItem')">
        <IconX size="16" class="hidden group-hover:block absolute -top-1.5 right-3 text-white p-px bg-rose-500 rounded-full hover:bg-rose-400 hover:scale-105 transition-all cursor-pointer" />
      </button>
    </div>
    <p v-if="mimeType?.includes('image')" class="text-sm text-muted !text-black max-w-24 text-center line-clamp-3 cursor-pointer hover:underline flex-1" @click="emit('openLightBox')">{{ name }}</p>
    <a v-else :href="url" target="_blank" rel="noopener noreferrer" class="text-sm text-muted !text-black max-w-24 text-center line-clamp-3 hover:underline flex-1">{{ name }}</a>
  </div>
</template>
