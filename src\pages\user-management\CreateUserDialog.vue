<script setup lang="ts">
import { reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { Button } from '@/shadcn-components/ui/button';
import Input from '@/shadcn-components/ui/inputs/Input.vue';
import Select from '@/shadcn-components/ui/inputs/Select.vue';
import { Label } from '@/shadcn-components/ui/label';
import adminApi from '@/util/adminAxios';
import { deployToast, ToastType } from '@/util/toast';
import type { RoleData } from '@/util/types/roles-and-permissions';

interface Props {
  allRoles: RoleData[]
}

const isOpened = defineModel<boolean>();
const { t } = useI18n();
const errors = ref<Record<string, string[]>>({});
const newUser = reactive({
  email: '',
  name: '',
  roles: [],
  eid: '',
  card: '',
  pin: '',
});

defineProps<Props>();

const emit = defineEmits(['userCreated']);

const onSubmit = async() => {
  try {
    await adminApi.post('/api/admin/users', newUser);
    newUser.email = '';
    newUser.name = '';
    newUser.roles = [];
    isOpened.value = false;
    emit('userCreated');
    deployToast(ToastType.SUCCESS, {
      text: t('user-management.user-create-success'),
      timeout: 6000,
    });
  } catch (e) {
    // @ts-ignore
    errors.value = e?.response?.data?.errors;
    deployToast(ToastType.ERROR, {
      text: t('user-management.user-create-fail'),
      timeout: 6000,
    });
  }
};
</script>

<template>
  <Teleport to="body">
    <el-dialog
      v-model="isOpened"
      width="fit-content"
      :show-close="false"
      header-class="hidden"
      class="!p-0 rounded-2xl"
    >
      <form autocomplete="off" class="bg-white p-6 rounded-2xl w-[35rem] max-w-[90vw]" @submit.prevent="onSubmit">
        <h3 class="font-bold text-xl mb-2">{{ $t('user-management.create-user') }}</h3>
        <p class="text-black/60 text-sm">
          {{ $t('user-management.create-user-desc') }}
        </p>
        <div class="grid gap-4 py-4">
          <div class="flex items-center gap-4">
            <Label for="new-user-name" class="text-right w-16">
              {{ $t('misc.name') }}
            </Label>
            <Input
              id="new-user-name"
              v-model="newUser.name"
              :errors="errors?.name"
              type="text"
              required
              class="ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0 focus-visible:border-gray-500"
            />
          </div>
          <div class="flex items-center gap-4">
            <Label for="new-user-mail" class="text-right w-16">
              {{ $t('misc.mail') }}
            </Label>
            <Input
              id="new-user-mail"
              v-model="newUser.email"
              type="email"
              :errors="errors?.email"
              required
              class="ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0 focus-visible:border-gray-500"
            />
          </div>
          <div class="flex items-center gap-4">
            <Label for="new-user-mail" class="text-right w-16">
              EID
            </Label>
            <Input
              id="new-user-mail"
              v-model="newUser.eid"
              :errors="errors?.eid"
              required
              class="ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0 focus-visible:border-gray-500"
            />
          </div>
          <div class="flex items-center gap-4">
            <Label for="new-user-mail" class="text-right w-16">
              {{ $t('user-management.card') }}
            </Label>
            <Input
              id="new-user-mail"
              v-model="newUser.card"
              :errors="errors?.card"
              required
              class="ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0 focus-visible:border-gray-500"
            />
          </div>
          <div class="flex items-center gap-4">
            <Label for="pin" class="text-right w-16">
              PIN
            </Label>
            <Input
              id="pin"
              v-model="newUser.pin"
              :errors="errors?.pin"
              required
              class="ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0 focus-visible:border-gray-500"
            />
          </div>
          <div class="flex items-center gap-4">
            <Label for="new-user-roles" class="text-right w-16">
              {{ $t('user-management.roles') }}
            </Label>
            <Select
              v-model="newUser.roles"
              :options="allRoles"
              :errors="errors?.roles"
              multiple
              item-value="id"
              collapse-tags
              item-title="name"
            />
          </div>
        </div>
        <div class="flex items-center justify-end gap-2">
          <Button type="button" variant="default" class="bg-gray-400/80 hover:bg-gray-400" @click="isOpened = false">
            <span>{{ $t('misc.cancel') }}</span>
          </Button>
          <Button type="submit">
            {{ $t('misc.save') }}
          </Button>
        </div>
      </form>
    </el-dialog>
  </Teleport>
</template>
