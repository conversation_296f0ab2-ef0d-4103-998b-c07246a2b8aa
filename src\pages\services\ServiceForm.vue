<script setup lang="ts">
import { IconPlus, IconTrash } from '@tabler/icons-vue';
import { Save } from 'lucide-vue-next';
import { computed, reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';
import { routeMap } from '@/router/routes';
import { Button } from '@/shadcn-components/ui/button';
import { Checkbox } from '@/shadcn-components/ui/checkbox';
import { Input, Textarea } from '@/shadcn-components/ui/inputs';
import { Label } from '@/shadcn-components/ui/label';
import adminApi from '@/util/adminAxios';
import { deployToast, ToastType } from '@/util/toast';
import type { MultiLang, Service, ServiceResource, ServiceVariant } from '@/util/types/api-responses';

const { t } = useI18n();
const route = useRoute();
const router = useRouter();

const isEdit = computed(() => !!route.params.id);
const errors = ref<Record<string, string[]>>({});

const service = reactive({
  name: {
    sk: '',
    en: '',
  } as MultiLang,
  description: {
    sk: '',
    en: '',
  } as MultiLang,
  capacity: 1,
  duration_minutes: 60,
  slot_time_duration: 30,
  active: true,
  use_resources: false,
  use_variants: false,
  resources: [] as ServiceResource[],
  variants: [] as ServiceVariant[],
});

// Load existing service data if editing
if (isEdit.value) {
  try {
    const { data } = await adminApi.get<{ data: Service }>(`/api/admin/reservations/services/${route.params.id}`);
    Object.assign(service, data.data);
  } catch (error) {
    deployToast(ToastType.ERROR, {
      text: t('misc.failed-to-get-data'),
      timeout: 6000,
    });
  }
}

const submit = async() => {
  try {
    if (isEdit.value) {
      await adminApi.put(`/api/admin/reservations/services/${route.params.id}`, service);
      deployToast(ToastType.SUCCESS, {
        text: t('services.service-updated'),
        timeout: 6000,
      });
    } else {
      await adminApi.post('/api/admin/reservations/services', service);
      deployToast(ToastType.SUCCESS, {
        text: t('services.service-created'),
        timeout: 6000,
      });
    }

    await router.replace({ name: routeMap.services.children.list.name });
  } catch (e: any) {
    errors.value = e?.response?.data?.errors || {};
    deployToast(ToastType.ERROR, {
      text: t('misc.error'),
      timeout: 6000,
    });
  }
};

const addResource = () => {
  service.resources.push({
    name: { sk: '', en: '' },
    active: true,
  });
};

const removeResource = (index: number) => {
  service.resources.splice(index, 1);
};

const addVariant = () => {
  service.variants.push({
    name: { sk: '', en: '' },
    active: true,
    duration_minutes: 30,
    only_resources: [],
  });
};

const removeVariant = (index: number) => {
  service.variants.splice(index, 1);
};
</script>

<template>
  <form @submit.prevent="submit">
    <div class="flex flex-col md:grid md:grid-cols-[2fr_1fr] gap-4">
      <div class="flex flex-col gap-4 mb-4">
        <h2 class="font-bold text-2xl text-paynes-gray-400">{{ $t('services.general-information') }}</h2>

        <div class="flex items-center gap-4">
          <Label for="name" class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32">
            {{ $t('services.name') }}
          </Label>
          <Input
            id="name"
            v-model="service.name"
            :errors="errors?.name"
            type="text"
            multilang
          />
        </div>

        <div class="flex items-start gap-4">
          <Label for="description" class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32 mt-2">
            {{ $t('services.description') }}
          </Label>
          <Textarea
            id="description"
            v-model="service.description"
            :errors="errors?.description"
            multilang
            rows="3"
          />
        </div>

        <div class="flex items-center gap-4">
          <Label for="capacity" class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32">
            {{ $t('services.capacity') }}
          </Label>
          <Input
            id="capacity"
            v-model.number="service.capacity"
            :errors="errors?.capacity"
            type="number"
            min="1"
          />
        </div>

        <div class="flex items-center gap-4">
          <Label for="duration" class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32">
            {{ $t('services.duration') }}
          </Label>
          <Input
            id="duration"
            v-model.number="service.duration_minutes"
            :errors="errors?.duration_minutes"
            type="number"
            min="1"
          />
        </div>

        <div class="flex items-center gap-4">
          <Label for="slot-duration" class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32">
            {{ $t('services.slot-time-duration') }}
          </Label>
          <Input
            id="slot-duration"
            v-model.number="service.slot_time_duration"
            :errors="errors?.slot_time_duration"
            type="number"
            min="1"
          />
        </div>

        <div class="flex items-center gap-4">
          <Label class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32">
            {{ $t('services.active') }}
          </Label>
          <Checkbox v-model:checked="service.active" />
        </div>

        <div class="flex items-center gap-4">
          <Label class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32">
            {{ $t('services.use-resources') }}
          </Label>
          <Checkbox v-model:checked="service.use_resources" />
        </div>

        <div class="flex items-center gap-4">
          <Label class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32">
            {{ $t('services.use-variants') }}
          </Label>
          <Checkbox v-model:checked="service.use_variants" />
        </div>
      </div>
    </div>

    <!-- Resources Section -->
    <div v-if="service.use_resources" class="mt-8">
      <div class="flex items-center justify-between mb-4">
        <h3 class="font-bold text-xl text-paynes-gray-400">{{ $t('services.resource-settings') }}</h3>
        <Button type="button" variant="outline" @click="addResource">
          <IconPlus class="w-4 h-4 mr-2" />
          {{ $t('services.add-resource') }}
        </Button>
      </div>

      <div v-for="(resource, index) in service.resources" :key="index" class="bg-gray-50 p-4 rounded-lg mb-4">
        <div class="flex items-center justify-between mb-4">
          <h4 class="font-medium">{{ $t('services.resource-name') }} {{ index + 1 }}</h4>
          <Button type="button" variant="outline" size="sm" class="text-red-600" @click="removeResource(index)">
            <IconTrash class="w-4 h-4" />
          </Button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label>{{ $t('services.resource-name') }}</Label>
            <Input v-model="resource.name" multilang />
          </div>
          <div class="flex items-center gap-2">
            <Label>{{ $t('services.active') }}</Label>
            <Checkbox v-model:checked="resource.active" />
          </div>
        </div>
      </div>
    </div>

    <!-- Variants Section -->
    <div v-if="service.use_variants" class="mt-8">
      <div class="flex items-center justify-between mb-4">
        <h3 class="font-bold text-xl text-paynes-gray-400">{{ $t('services.variant-settings') }}</h3>
        <Button type="button" variant="outline" @click="addVariant">
          <IconPlus class="w-4 h-4 mr-2" />
          {{ $t('services.add-variant') }}
        </Button>
      </div>

      <div v-for="(variant, index) in service.variants" :key="index" class="bg-gray-50 p-4 rounded-lg mb-4">
        <div class="flex items-center justify-between mb-4">
          <h4 class="font-medium">{{ $t('services.variant-name') }} {{ index + 1 }}</h4>
          <Button type="button" variant="outline" size="sm" class="text-red-600" @click="removeVariant(index)">
            <IconTrash class="w-4 h-4" />
          </Button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label>{{ $t('services.variant-name') }}</Label>
            <Input v-model="variant.name" multilang />
          </div>
          <div>
            <Label>{{ $t('services.variant-duration') }}</Label>
            <Input v-model.number="variant.duration_minutes" type="number" min="1" />
          </div>
          <div class="flex items-center gap-2">
            <Label>{{ $t('services.active') }}</Label>
            <Checkbox v-model:checked="variant.active" />
          </div>
        </div>
      </div>
    </div>

    <div class="flex justify-end mt-8">
      <Button class="w-fit space-x-2 bg-green-600 hover:bg-green-700 ml-auto" @click.prevent="submit">
        <Save v-if="isEdit" stroke-width="1.5" />
        <span>{{ isEdit ? $t('misc.save') : $t('misc.create') }}</span>
      </Button>
    </div>
  </form>
</template>
