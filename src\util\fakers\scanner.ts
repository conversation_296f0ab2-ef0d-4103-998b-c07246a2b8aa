export enum FakeReaderActions {
    DATA_FROM_READER = 'dataFromReader',
}

export const fakeCardRead = (action: string, data: any) => {
  if (import.meta.env.DEV) {
    const response = {
      'action': action,
      'data': data,
      'inputDevice': {
        'name': 'StrongLink USB CardReader',
        'id': 6,
        'vendorId': 1241,
        'productId': 5379,
        'descriptor': 'ecb58b2358483198f0a3c079c9e3b4c60760f2a7',
        'keyboardType': 2,
        'sources': '-2147483391 (KEYBOARD)',
        'controllerNumber': 0,
        'isVirtual': false,
        'hasMicrophone': false,
      },
    };
    window.receiveResponse(JSON.stringify(response));
  }
};

export const generateFakeCardReadFn = (action: string, data: any) => {
  return () => fakeCardRead(action, data);
};
