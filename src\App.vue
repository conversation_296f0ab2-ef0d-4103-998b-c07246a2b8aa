<script setup lang="ts">
import en from 'element-plus/es/locale/lang/en';
import sk from 'element-plus/es/locale/lang/sk';
import zh from 'element-plus/es/locale/lang/zh-cn';
import { computed, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';
import { useAuthStore } from '@/stores/auth-store';

const { locale: i18nLocale } = useI18n();

const route = useRoute();
const store = useAuthStore();
const loading = ref(false);
const loadingPane = ref();

watch(i18nLocale, (newVal) => {
  store.currentInputLang = newVal;
});

const localeMap = {
  'en': en,
  'sk': sk,
  'zh': zh,
};
const locale = computed(() => localeMap[(localStorage.getItem('appLang') ?? navigator.languages[1]) ?? 'sk']);
</script>

<template>
  <template v-if="route.name !== 'not-found'">
    <component :is="$layout">
      <el-config-provider :locale="locale">
        <router-view v-slot="{ Component }">
          <Suspense timeout="0">
            <template #default>
              <component :is="Component" />
            </template>
            <template #fallback>
              <transition name="fade" appear>
                <div class="fullscreen bg-black flex-col text-white">
                  <div class="w-32">
                    <div class="loader w-full text-xs flex justify-between mb-2">
                      <span v-for="char in [...$t('misc.loading')?.replaceAll(' ', '&nbsp;')]" :key="char">{{ char }}</span>
                    </div>
                  </div>
                </div>
              </transition>
            </template>
          </Suspense>
        </router-view>
      </el-config-provider>
    </component>
    <transition name="fade" appear>
      <div v-if="loading" ref="loadingPane" class="fullscreen bg-black flex-col text-white">
        <div class="w-32">
          <div class="loader w-full text-xs flex justify-between mb-2">
            <span v-for="char in [...$t('misc.loading')?.replaceAll(' ', '&nbsp;')]" :key="char">{{ char }}</span>
          </div>
        </div>
      </div>
    </transition>
  </template>
  <template v-else>
    <router-view />
  </template>
</template>

<style>
.v-enter-active,
.v-leave-active {
  transition: opacity 0.4s ease;
}

.v-enter-from,
.v-leave-to {
  opacity: 0;
}
</style>

<style scoped lang="scss">
.disable-scroll {
  position: absolute;
  left: 0;
  top: 0;
  height: 100vh;
  overflow: hidden;
}
.fullscreen {
  position: fixed;
  z-index: 10000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  //background: linear-gradient(90deg, rgba(0,0,0,1) 0%, $gradient-blue 100%);
  visibility: visible;
  opacity: 1;
  transition: opacity 2s linear;
  &.faded-out {
    opacity: 0;
    transition: opacity .5s linear;
  }
}

.fade-leave-active {
  transition: opacity 0.44s ease;
}

.fade-enter-from {
  opacity: 1;
}
.fade-leave-to {
  opacity: 0;
}
</style>

<style scoped lang="scss">
.loader {
  position: relative;
  // width: fit-content;

  span {
    display: inline-block;
    opacity: 0;
    animation: textLoader 2s infinite;
  }

  @for $i from 1 through 40 {
    span:nth-child(#{$i}) {
      animation-delay: ($i - 1) * 0.1s;
    }
  }
}

@keyframes textLoader {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
</style>

