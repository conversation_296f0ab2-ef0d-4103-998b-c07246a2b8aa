<script setup lang="ts">
import { computed, ref } from 'vue';
import Keypad from '@/pages/cash-register/dialogs/components/Keypad.vue';
import { useCashRegisterStore } from '@/stores/cash-register';
import cashApi from '@/util/cashAxios';
import { getFromMultiLangObject } from '@/util/multilang';
import type { CROrderItem } from '@/util/types/api-responses';

const store = useCashRegisterStore();

const emit = defineEmits(['close']);
const props = defineProps<{ product: CROrderItem }>();

const discount = ref<string[]>((props.product ? props.product?.discount_amount?.toString()?.split('') : store.order?.discount_amount?.toString()?.split('')) ?? []);

const apply = async(cancelDiscount = false) => {
  const discount_amount = Number(discount.value.join(''));

  if (props.product) {
    await cashApi.put(`/api/cash-register/orders/${store.order?.id}/items/${props.product.id}`, {
      quantity: props.product.quantity,
      discount_type: cancelDiscount ? null : 'PERCENT',
      discount_amount,
    });
  } else {
    await cashApi.put(`/api/cash-register/orders/${store.order?.id}`, {
      discount_type: cancelDiscount ? null : 'PERCENT',
      discount_amount,
    });
  }

  store.loadCart();
  emit('close');
};

const originalPrice = computed(() => {
  if (props.product) {
    return Number(props.product.price);
  }
  return store.order?.items?.reduce((acc, item) => acc + item.price_calculated, 0) ?? 0;
});
const afterDiscount = computed(() => Number(originalPrice.value) * (1 - Number(discount.value.join('')) / 100));
</script>

<template>
  <div class="fixed inset-0 z-50 bg-black/65 grid place-items-center font-medium" @click.self="emit('close')">
    <div class="max-w-[520px] w-full bg-white p-12 rounded-xl text-xl">
      <h2 v-if="product" class="text-xl font-bold mb-4">{{ $t('cash-register.apply-discount-for') }}: {{ product.quantity }}x {{ getFromMultiLangObject(product?.name) }}</h2>
      <h2 v-else class="text-xl font-bold mb-4">{{ $t('cash-register.apply-discount') }}</h2>

      <div class="bg-slate-200 p-5 rounded-xl space-y-1">
        <div class="flex justify-between items-center">
          <div class="font-normal text-slate-500">{{ $t('cash-register.original-price') }}:</div>
          <div class="font-bold text-2xl">{{ originalPrice.toFixed(2) }} €</div>
        </div>
        <div class="flex justify-between items-center">
          <div class="font-normal text-slate-500">{{ $t('cash-register.after-discount') }}:</div>
          <div :class="[afterDiscount !== originalPrice && 'text-emerald-500', 'font-bold text-2xl']">{{ afterDiscount.toFixed(2) }} €</div>
        </div>
        <div class="text-center font-bold text-3xl !mt-4">
          {{ Number(discount.join('')) }}%
        </div>
      </div>
      <div class="grid grid-cols-[4fr_1fr] gap-2 mt-5">
        <Keypad v-model="discount" percent class="w-full" />
        <div class="grid gap-2">
          <button v-for="disc in ['5', '10', '15', '20', '50', '100']" :key="disc" class="font-medium rounded-lg active:scale-[0.99] bg-slate-100 border border-black/5" @click="discount = disc.split('')">
            {{ disc }}%
          </button>
        </div>
      </div>
      <div class="flex gap-2">
        <button v-if="props.product ? props.product.discount_amount : store.order?.discount_amount" class="w-fit text-nowrap mt-5 bg-orange-400 text-white py-4 px-4 rounded-lg" @click="apply(true)">
          {{ $t('cash-register.cancel-discount') }}
        </button>
        <button :disabled="Number(discount.join('')) < 1" class="w-full mt-5 disabled:grayscale bg-emerald-500 text-white py-4 rounded-lg" @click="apply()">
          {{ $t('misc.confirm') }}
        </button>
      </div>
    </div>
  </div>
</template>
