<script lang="ts" setup>
import { X } from 'lucide-vue-next';
</script>

<template>
  <div class="rounded-full flex items-center justify-center animate-check bg-red-500 overflow-hidden will-change-transform">
    <X class="x-icon transition-all duration-100 w-auto h-4/6 text-white" />
  </div>
</template>

<style scoped>

@keyframes animate-check-fgpw {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes animate-check-icon-fgpw {
  0% {
    opacity: 0;
    transform: translateX(-25%);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-check {
  opacity: 0;
  animation: animate-check-fgpw forwards 1s ease-out;
  animation-delay: 0.25s;
  .x-icon {
    opacity: 0;
    transform: translateX(-10%);
    animation: animate-check-icon-fgpw forwards 0.5s ease-out;
    animation-delay: 0.55s;
  }
}

</style>
