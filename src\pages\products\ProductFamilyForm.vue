<script setup lang="ts">
import { reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';
import { routeMap } from '@/router/routes';
import Button from '@/shadcn-components/ui/button/Button.vue';
import Input from '@/shadcn-components/ui/inputs/Input.vue';
import { Label } from '@/shadcn-components/ui/label';
import AttachmentsUpload from '@/shadcn-components/ui/uploaders/AttachmentsUpload.vue';
import adminApi from '@/util/adminAxios';
import { formatDate } from '@/util/datetime';
import { deployToast, ToastType } from '@/util/toast';
import type { MultiLang, AttachmentType } from '@/util/types/api-responses';

const route = useRoute();
const router = useRouter();
const { t } = useI18n();

const isEdit = (route.name as string).includes('edit');
const lastEdited = ref<string | null>(null);
const types = ref<AttachmentType[]>([]);
const family = reactive({
  attachments: [],
  name: {
    sk: '',
    en: '',
    zh: '',
  } as MultiLang,
});
const errors = ref<Record<string, string[]>>({});

const submit = async() => {
  try {
    if (isEdit) {
      await adminApi.put(`/product-families/${route.params.id}`, family);
    } else {
      await adminApi.post('/product-families', family);
    }

    deployToast(ToastType.SUCCESS, {
      text: t('misc.form-sent-successfully'),
      timeout: 6000,
    });
    await router.replace({ name: routeMap.products.children.families.name });
  } catch (e) {
    // @ts-ignore
    errors.value = e?.response?.data?.errors;
    deployToast(ToastType.ERROR, {
      text: t('misc.error'),
      timeout: 6000,
    });
  }
};

if (isEdit) {
  const { data } = await adminApi.get(`/product-families/${route.params.id}`);
  family.attachments = data.data.attachments;
  family.name = data.data.name;

  lastEdited.value = data.data.updated;
}

const { data: tps } = await adminApi.get('/attachment/types', { params: { model: 'product' }});
types.value = tps.data;
</script>

<template>
  <form class="flex flex-col gap-4" @submit.prevent>
    <div v-if="lastEdited" class="flex items-center text-sm font-medium leading-nones gap-2 text-neutral-500">
      <div>{{ $t('products.last-modified') }}:</div>
      <div>{{ formatDate(lastEdited) }}</div>
    </div>

    <div class="flex items-center gap-4">
      <Label class="text-right">
        {{ $t('products.name') }}
      </Label>
      <div class="grid grid-cols-1 md:grid-cols-3 w-full gap-2">
        <Input
          v-for="lng in ['sk', 'en', 'zh']"
          :key="lng"
          v-model="family.name[lng]"
          :errors="errors.name"
          type="text"
          required
          class="ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0 focus-visible:border-gray-500 pl-8"
          :class="[lng === 'zh' ? '!pl-11' : 'pl-8']"
          width="100%"
        >
          <template #left>
            <div class="absolute left-2 top-1/2 -translate-y-1/2 font-medium text-black/40 select-none uppercase">{{ lng === 'zh' ? '中文' : lng }}</div>
          </template>
        </Input>
      </div>
    </div>

    <AttachmentsUpload :types :errors :attachments="family.attachments" @get-attachments="args => family.attachments = args" />

    <Button class="w-fit" @click.prevent="submit">{{ isEdit ? $t('misc.save') : $t('misc.create') }}</Button>
  </form>
</template>
