<script setup lang="ts">
import { IconCircleCheckFilled, IconCircleXFilled } from '@tabler/icons-vue';
import { useRouteQuery } from '@vueuse/router';
import { debounce } from 'lodash-es';
import { Check, Edit, PlusIcon, Search, Trash2, X } from 'lucide-vue-next';
import { reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { AlertDialog, AlertDialogHeader, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogFooter, AlertDialogTitle, AlertDialogTrigger } from '@/shadcn-components/ui/alert-dialog';
import Button from '@/shadcn-components/ui/button/Button.vue';
import { Input, Textarea } from '@/shadcn-components/ui/inputs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/shadcn-components/ui/table';
import { useAuthStore } from '@/stores/auth-store';
import adminApi from '@/util/adminAxios';
import { deployToast, ToastType } from '@/util/toast';
import type { CustomerGroup, Meta, Response } from '@/util/types/api-responses';
import { ComponentStateType } from '@/util/types/components';

const { t } = useI18n();
const authStore = useAuthStore();
const currentPage = useRouteQuery('page');
const currentLimit = useRouteQuery('limit');

const creatingNew = ref(false);
const componentState = ref(ComponentStateType.LOADING);
const groups = ref<CustomerGroup[]>([]);
const paginationMeta = ref<Meta>();
const editingId = ref<string>();
const editingData = ref<Partial<CustomerGroup>>({});

const newItem = reactive<Partial<CustomerGroup>>({});

const fetchData = async(args?: { page?: number, limit?: number, search?: string }) => {
  const to = setTimeout(() => {
    componentState.value = ComponentStateType.LOADING;
  }, 220);

  try {
    const params = {
      page: (args?.page ?? currentPage.value) ?? 1,
      limit: (args?.limit ?? currentLimit.value) ?? 15,
    } as Record<string, string | number>;
    if (args?.search && args.search.length > 2) {
      params.search = args.search;
      delete params.page;
    }

    const { data: { data, meta }} = await adminApi.get<Response<CustomerGroup[]>>('/api/admin/customers/groups', { params });

    groups.value = data;
    paginationMeta.value = meta;
    clearTimeout(to);
    componentState.value = ComponentStateType.OK;
  } catch {
    clearTimeout(to);
    componentState.value = ComponentStateType.ERROR;
  }
};

await fetchData();

const startEdit = (group: CustomerGroup) => {
  editingId.value = group.id;
  editingData.value = { ...group };
};

const cancelEdit = () => {
  editingId.value = undefined;
  editingData.value = {};
};

const saveEdit = async() => {
  if (!editingId.value) {
    return;
  }

  try {
    await adminApi.put(`/api/admin/customers/groups/${editingId.value}`, editingData.value);
    await fetchData();
    editingId.value = undefined;
    editingData.value = {};
  } catch {
    // Handle error
  }
};

const createNewItem = async() => {
  try {
    await adminApi.post('/api/admin/customers/groups', newItem);
    await fetchData();
    resetNewItem();
  } catch {
    deployToast(ToastType.ERROR, {
      text: t('misc.error'),
      timeout: 6000,
    });
  }
};

const resetNewItem = () => {
  newItem.name = '';
  newItem.description = '';
  newItem.discount_percent = undefined;
  newItem.active = false;
  newItem.can_invoice_order = false;
  creatingNew.value = false;
};

const onSearchInput = debounce((ev: Record<any, any>) => {
  fetchData({ search: ev.target?.value });
}, 500);

const removeGroup = async(id: string) => {
  try {
    await adminApi.delete(`/api/admin/customers/groups/${id}`);
    await fetchData();
  } catch {
    // Handle error
  }
};
</script>

<template>
  <div class="flex flex-col">
    <div v-if="componentState === ComponentStateType.OK" class="flex flex-col">
      <div class="flex items-center gap-4 mb-2 flex-wrap">
        <Button v-if="!creatingNew" :class="{ 'pointer-events-none select-none opacity-50': !authStore.hasPermission('customer manage') }" class="w-fit bg-gray-200 rounded-lg p-2 hover:bg-gray-300 transition-colors gap-0.5 flex items-center cursor-pointer" @click="creatingNew = true">
          <PlusIcon class="aspect-square text-gray-500" />
          <div class="text-sm font-medium text-gray-600">{{ $t('customers.new-group') }}</div>
        </Button>
        <div class="relative p-2 bg-white border-gray-300 border rounded-lg flex items-center w-64 overflow-hidden flex-1 sm:flex-none">
          <div class="pl-0.5 pr-1.5">
            <Search class="min-h-5 h-5 min-w-5 w-5 text-muted-foreground bg-mute" />
          </div>
          <input
            type="text"
            :placeholder="$t('misc.search')"
            class="w-full focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-gray-500 outline-none placeholder:text-sm"
            @input="onSearchInput"
          >
        </div>
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead class="text-center">#</TableHead>
            <TableHead>{{ $t('products.name') }}</TableHead>
            <TableHead>{{ $t('misc.description') }}</TableHead>
            <TableHead>{{ $t('cash-register.discount') }}</TableHead>
            <TableHead>{{ $t('products.active') }}</TableHead>
            <TableHead>{{ $t('customers.can_invoice_order') }}</TableHead>
            <TableHead class="text-right">{{ $t('user-management.actions') }}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-if="creatingNew" class="bg-yellow-100 hover:bg-yellow-100">
            <TableCell />
            <TableCell>
              <Input
                v-model="newItem.name"
              />
            </TableCell>
            <TableCell>
              <Textarea
                v-model="newItem.description"
                height="48"
              />
            </TableCell>
            <TableCell>
              <Input
                v-model="newItem.discount_percent"
                type="number"
              />
            </TableCell>
            <TableCell>
              <el-switch
                v-model="newItem.active"
                style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
              />
            </TableCell>
            <TableCell>
              <el-switch
                v-model="newItem.can_invoice_order"
                style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
              />
            </TableCell>
            <TableCell class="text-right">
              <div class="flex justify-end items-center flex-nowrap gap-2">
                <Button class="size-8 p-1.5 rounded-full bg-green-500 hover:bg-green-600" variant="default" @click="createNewItem">
                  <Check class="w-full h-full" />
                </Button>
                <Button class="size-8 p-1.5 rounded-full " variant="destructive" @click="resetNewItem">
                  <X class="w-full h-full" />
                </Button>
              </div>
            </TableCell>
          </TableRow>

          <TableRow v-for="(group, index) in groups" :key="group.id">
            <TableCell class="text-center">{{ index + 1 }}</TableCell>
            <TableCell>
              <Input
                v-if="editingId === group.id"
                v-model="editingData.name"
                class="w-full"
              />
              <span v-else>{{ group.name }}</span>
            </TableCell>
            <TableCell>
              <Textarea
                v-if="editingId === group.id"
                v-model="editingData.description"
                class="w-full"
                height="48"
              />
              <span v-else>{{ group.description }}</span>
            </TableCell>
            <TableCell>
              <Input
                v-if="editingId === group.id"
                v-model="editingData.discount_percent"
                type="number"
                min="0"
                max="100"
                class="w-24"
              />
              <span v-else>{{ group.discount_percent }}%</span>
            </TableCell>
            <TableCell>
              <el-switch
                v-if="editingId === group.id"
                v-model="editingData.active"
                style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
              />
              <IconCircleCheckFilled v-else-if="group.active" class="text-green-500" />
              <IconCircleXFilled v-else class="text-red-500" />
            </TableCell>

            <TableCell>
              <el-switch
                v-if="editingId === group.id"
                v-model="editingData.can_invoice_order"
                style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
              />
              <IconCircleCheckFilled v-else-if="group.can_invoice_order" class="text-green-500" />
              <IconCircleXFilled v-else class="text-red-500" />
            </TableCell>

            <TableCell class="text-right flex gap-2 justify-end">
              <template v-if="editingId === group.id">
                <Button size="sm" class="text-xs" @click="saveEdit">
                  {{ $t('turnstiles.save') }}
                </Button>
                <Button variant="outline" size="sm" class="text-xs" @click="cancelEdit">
                  {{ $t('turnstiles.cancel') }}
                </Button>
              </template>
              <template v-else>
                <Button class="size-8 p-1.5 rounded-full bg-paynes-gray text-primary-foreground hover:bg-paynes-gray/90" @click="startEdit(group)">
                  <Edit />
                </Button>
                <AlertDialog>
                  <AlertDialogTrigger :disabled="!authStore.hasPermission('customer manage')" @click.prevent.stop>
                    <Button class="w-8 h-8 p-1.5 rounded-full" variant="destructive">
                      <Trash2 class="w-full h-full" />
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>{{ $t('form.delete-group', { name: group.name }) }}</AlertDialogTitle>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>{{ $t('misc.cancel') }}</AlertDialogCancel>
                      <AlertDialogAction class="bg-destructive text-destructive-foreground hover:bg-destructive/90" @click="removeGroup(group.id)">{{ $t('misc.continue') }}</AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </template>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  </div>
</template>
