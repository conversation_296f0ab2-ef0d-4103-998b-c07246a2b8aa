<script setup lang="ts">
import { computed, nextTick, onBeforeUnmount, ref, watch } from 'vue';
import AntikLogo from '@/assets/svg/antik_logo.svg';
import { useCashRegisterStore } from '@/stores/cash-register';
import { CustomEvents } from '@/util/facades/custom-event';
import { getFromMultiLangObject } from '@/util/multilang';
import type { Tax } from '@/util/types/api-responses';

const store = useCashRegisterStore();

const listRef = ref<HTMLElement>();

const onCashRegisterEvent = (e: Event) => {
  const { detail } = e as CustomEvent<{ action: string }>;
  if (['order_paid', 'order_cancelled'].includes(detail.action)) {
    store.clearCart();
  }
};

watch(() => store.order?.items?.length, () => {
  nextTick(() => {
    if (listRef.value) {
      listRef.value.scrollTop = listRef.value.scrollHeight;
    }
  });
});

window.eventBus.addEventListener(CustomEvents.CashRegisterFrontendEvent, onCashRegisterEvent);

onBeforeUnmount(() => {
  window.eventBus.removeEventListener(CustomEvents.CashRegisterFrontendEvent, onCashRegisterEvent);
});

const vat = computed(() => store.order?.items?.reduce((sum, item) => sum + item.price_calculated * ((item.product.tax_id as unknown as Tax).rate / 100), 0) ?? 0);
</script>

<template>
  <main class="bg-black text-white">
    <div v-if="store.order?.items?.length" class="grid h-screen grid-cols-1 sm:grid-cols-[45%_1fr]">
      <section ref="listRef" class="p-4 sm:p-6 space-y-3 flex-1 overflow-y-scroll">
        <div v-for="item in store.order?.items" :key="item.id" class="flex items-center justify-between border-b border-neutral-600 pb-3">
          <div>
            <div class="fig text-xl sm:text-2xl">
              <div class="text-neutral-200">{{ item.quantity }}x</div>
              <div class="font-medium line-clamp-2">{{ getFromMultiLangObject(item.product.name) }}</div>
            </div>
            <div v-if="item.discount_amount" class="fig gap-1.5 sm:text-lg">
              <span class="text-neutral-400 line-through">{{ item.price_unit.toFixed(2) }} €</span>
              <span class="text-emerald-500">{{ (item.price_calculated / item.quantity).toFixed(2) }} €</span>
            </div>
            <div v-else-if="item.quantity > 1" class="text-neutral-400 sm:text-lg">{{ item.product.price }} €</div>
          </div>

          <div>
            <div v-if="item.price !== item.price_calculated" class="text-lg sm:text-xl text-neutral-400 font-medium text-right line-through -mb-1 mt-1 whitespace-nowrap">{{ (item.price).toFixed(2) }} €</div>
            <div class="whitespace-nowrap" :class="[item.price !== item.price_calculated && 'text-emerald-500', 'text-xl sm:text-2xl font-bold']">{{ (item.price_calculated).toFixed(2) }} €</div>
          </div>
        </div>
      </section>

      <section class="flex flex-col mt-auto sm:mt-0">
        <div class="hidden flex-1 sm:grid place-content-center">
          <AntikLogo class="w-full" />
        </div>

        <div class="p-4 sm:p-6 space-y-4 sm:w-2/3 sm:ml-auto">
          <h2 class="text-3xl sm:text-4xl font-bold">{{ $t('cash-register.summary') }}</h2>
          <div class="space-y-2 text-xl sm:text-2xl">
            <div class="flex items-center justify-between">
              <div class="text-neutral-400">{{ $t('cash-register.subtotal') }}</div>
              <div class="font-medium">{{ (store.order.price - store.order.discount_calculated_order_items).toFixed(2) }} €</div>
            </div>
            <div v-if="store.order.discount_calculated" class="flex items-center justify-between">
              <div class="text-neutral-400">{{ $t('cash-register.discount-total') }}</div>
              <div class="font-medium text-emerald-400">-{{ store.order.discount_calculated_order.toFixed(2) }} €</div>
            </div>
            <div class="flex items-center justify-between">
              <div class="text-neutral-400">{{ $t('cash-register.vat') }}</div>
              <div class="font-medium">{{ vat.toFixed(2) }} €</div>
            </div>
          </div>
          <div class="flex items-center justify-between border-t pt-4 border-neutral-600 font-bold text-3xl sm:text-4xl">
            <div>{{ $t('cash-register.total') }}</div>
            <div class="text-green-400">{{ store.order.price_calculated.toFixed(2) }} €</div>
          </div>
        </div>
      </section>
    </div>

    <div v-else class="h-screen w-full grid place-content-center">
      <AntikLogo class="w-full" />
    </div>
  </main>
</template>
