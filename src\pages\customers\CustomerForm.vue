<script setup lang="ts">
import { IconTrash } from '@tabler/icons-vue';
import { Save } from 'lucide-vue-next';
import { computed, reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';
import { routeMap } from '@/router/routes';
import Button from '@/shadcn-components/ui/button/Button.vue';
import { Input, Select } from '@/shadcn-components/ui/inputs';
import Label from '@/shadcn-components/ui/label/Label.vue';
import { useAuthStore } from '@/stores/auth-store';
import adminApi from '@/util/adminAxios';
import { deployToast, ToastType } from '@/util/toast';
import type { Customer, CustomerCard, CustomerGroup } from '@/util/types/api-responses';

const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();

const isEdit = (route.name as string).includes('edit');
const groups = ref<CustomerGroup[]>([]);
const cardTypes = [{ value: 'rfid', label: 'RFID' }, { value: 'mifare', label: 'MIFARE' }, { value: 'qrcode', label: 'QR' }] as const;
const form = reactive({
  name: undefined as unknown as string,
  address: undefined as unknown as string,
  zipcode: undefined as unknown as string,
  city: undefined as unknown as string,
  email: undefined as unknown as string,
  phone: undefined as unknown as string,
  active: true,
  discount_percent: 0,
  can_invoice_order: false,
  customer_groups: [] as (string[] | CustomerGroup[]),
  cards: [] as CustomerCard[],
});
const errors = ref<Record<string, string[]>>({});

const submit = async() => {
  try {
    form.cards = form.cards.filter(card => card.card_number && card.card_type);

    if (isEdit) {
      await adminApi.put(`/api/admin/customers/${route.params.id}`, form);
    } else {
      await adminApi.post('/api/admin/customers', form);
    }

    deployToast(ToastType.SUCCESS, {
      text: t('misc.form-sent-successfully'),
      timeout: 6000,
    });
    await router.replace({ name: routeMap.customers.children.list.name });
  } catch (e) {
    // @ts-ignore
    errors.value = e?.response?.data?.errors;
    deployToast(ToastType.ERROR, {
      text: t('misc.error'),
      timeout: 6000,
    });
  }
};

if (isEdit) {
  const { data: { data }} = await adminApi.get<{ data: Customer }>(`/api/admin/customers/${route.params.id}`);
  Object.assign(form, data);
  form.customer_groups = data.customer_groups?.map(group => group.id) ?? [];
}

try {
  const [
    { data: grps },
  ] = await Promise.all([
    adminApi.get('/api/admin/customers/groups'),
  ]);

  groups.value = grps.data;
} catch (e) {
  deployToast(ToastType.ERROR, {
    text: t('misc.error'),
    timeout: 6000,
  });
}

const disableByPermission = computed(() => !authStore.hasPermission('customer manage'));
</script>

<template>
  <form @submit.prevent>
    <div class="flex flex-col md:grid md:grid-cols-2 gap-4">
      <div class="flex flex-col gap-4 mb-4">
        <h2 class="font-bold text-2xl text-paynes-gray-400">{{ $t('products.general-information') }}</h2>

        <div class="flex items-center gap-4">
          <Label class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32">
            {{ $t('misc.name') }}
          </Label>
          <Input
            v-model="form.name"
            :errors="errors?.name"
            :disabled="disableByPermission"
          />
        </div>
        <div class="flex items-center gap-4">
          <Label class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32">
            {{ $t('misc.mail') }}
          </Label>
          <Input
            v-model="form.email"
            :errors="errors?.email"
            :disabled="disableByPermission"
          />
        </div>
        <div class="flex items-center gap-4">
          <Label class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32">
            {{ $t('misc.phone') }}
          </Label>
          <Input
            v-model="form.phone"
            :errors="errors?.phone"
            :disabled="disableByPermission"
          />
        </div>
        <div class="flex items-center gap-4">
          <Label class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32">
            {{ $t('misc.address') }}
          </Label>
          <Input
            v-model="form.address"
            :errors="errors?.address"
            :disabled="disableByPermission"
          />
        </div>
        <div class="flex items-center gap-4">
          <Label class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32">
            {{ $t('misc.city') }}
          </Label>
          <Input
            v-model="form.city"
            :errors="errors?.city"
            :disabled="disableByPermission"
          />
        </div>
        <div class="flex items-center gap-4">
          <Label class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32">
            {{ $t('misc.zipcode') }}
          </Label>
          <Input
            v-model="form.zipcode"
            :errors="errors?.zipcode"
            :disabled="disableByPermission"
          />
        </div>
      </div>

      <div class="flex flex-col gap-4 mb-4">
        <h2 class="font-bold text-2xl text-paynes-gray-400">{{ $t('customers.customer-settings') }}</h2>

        <div class="flex items-center gap-4">
          <Label class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32">
            {{ $t('products.active') }}
          </Label>
          <el-switch
            v-model="form.active"
            :errors="errors?.active"
            :disabled="disableByPermission"
            style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
          />
        </div>
        <div class="flex items-center gap-4">
          <Label class="text-right min-w-44 sm:w-48 sm:min-w-32">
            {{ $t('customers.can_invoice_order') }}
          </Label>
          <el-switch
            v-model="form.can_invoice_order"
            :errors="errors?.can_invoice_order"
            :disabled="disableByPermission"
            style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
          />
        </div>
        <div class="flex items-center gap-4">
          <Label class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32">
            {{ $t('cash-register.discount') }} (%)
          </Label>
          <Input
            v-model="form.discount_percent"
            type="number"
            step="0.5"
            :errors="errors?.discount_percent"
            :disabled="disableByPermission"
          />
        </div>
        <div class="flex items-center gap-4">
          <Label class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32">
            {{ $t('customers.customer_groups') }}
          </Label>
          <Select
            v-model="form.customer_groups"
            :options="groups"
            item-value="id"
            item-title="name"
            multiple
            :errors="errors?.customer_groups"
            :disabled="disableByPermission"
          />
        </div>
      </div>
    </div>

    <div>
      <div class="fig gap-4 mb-4">
        <h2 class="font-bold text-2xl text-paynes-gray-400">{{ $t('customers.customer-cards') }}</h2>
        <Button :disabled="disableByPermission" @click.prevent="form.cards.push({ card_number: undefined as unknown as string, card_type: undefined as unknown as string, active: false })">
          <span>{{ $t('customers.add-card') }}</span>
        </Button>
      </div>

      <div class="flex flex-wrap gap-4">
        <div v-for="(card, idx) in form.cards" :key="idx" class="border bg-white w-full max-w-[400px] rounded-2xl p-5 mb-4">
          <div class="flex items-center justify-between mb-6">
            <div class="fig">
              <div class="text-xl font-bold">{{ $t('user-management.card') }} #{{ idx + 1 }}</div>
              <button class="text-red-500 hover:text-red-700" :disabled="disableByPermission" @click.prevent="form.cards.splice(idx, 1)">
                <IconTrash />
              </button>
            </div>
            <el-switch
              v-model="card.active"
              :disabled="disableByPermission"
              style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
            />
          </div>

          <div class="grid gap-4">
            <div>
              <Label class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32">
                {{ $t('customers.card-number') }}
              </Label>
              <Input
                v-model="card.card_number"
                :disabled="disableByPermission"
              />
            </div>
            <div>
              <Label class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32">
                {{ $t('customers.card-type') }}
              </Label>
              <Select
                v-model="card.card_type"
                :options="cardTypes"
                item-value="value"
                item-title="label"
                :disabled="disableByPermission"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="flex justify-end mt-8">
      <Button :disabled="disableByPermission" class="w-fit space-x-2 bg-green-600 hover:bg-green-700 ml-auto" @click.prevent="submit">
        <Save v-if="isEdit" stroke-width="1.5" />
        <span>{{ isEdit ? $t('misc.save') : $t('misc.create') }}</span>
      </Button>
    </div>
  </form>
</template>
