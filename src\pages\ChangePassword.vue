<script setup lang="ts">
import { reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';
import PasswordHelper from '@/components/forms/PasswordHelper.vue';
import PageLoader from '@/components/global/PageLoader.vue';
import { routeMap } from '@/router/routes';
import { Button } from '@/shadcn-components/ui/button';
import { InputPassword } from '@/shadcn-components/ui/input-password';
import { Label } from '@/shadcn-components/ui/label';
import adminApi from '@/util/adminAxios';
import { initializeBasicBreadcrumbBehaviour } from '@/util/facades/breadcrumb';
import { deployToast, ToastType } from '@/util/toast';
import { ComponentStateType } from '@/util/types/components';

const route = useRoute();
const componentState = ref(ComponentStateType.OK);
const { t } = useI18n();

initializeBasicBreadcrumbBehaviour(routeMap.settings.children.changePassword.meta.i18nTitle, routeMap.settings.children.changePassword.name, false, route);

const credentials = reactive({
  currentPassword: '',
  newPassword: '',
  repeatedNewPassword: '',
});

const onResetPasswordRequest = async() => {
  componentState.value = ComponentStateType.LOADING;
  try {
    await adminApi.post('/user/change-password', {
      password_current: credentials.currentPassword,
      password: credentials.newPassword,
      password_confirmation: credentials.repeatedNewPassword,
    });
    credentials.currentPassword = '';
    credentials.newPassword = '';
    credentials.repeatedNewPassword = '';
    componentState.value = ComponentStateType.OK;
    deployToast(ToastType.SUCCESS, {
      text: t('login.reset-pw-succ'),
      timeout: 6000,
    });
  } catch {
    deployToast(ToastType.ERROR, {
      text: t('login.failed-to-reset-pw'),
      timeout: 6000,
    });
  }
  componentState.value = ComponentStateType.OK;
};

</script>

<template>
  <div class="p-4 relative">
    <form class="grid gap-4 w-60 lw:w-80" @submit.prevent="onResetPasswordRequest">
      <div class="grid gap-2">
        <div class="flex items-center">
          <Label for="password">{{ $t('login.current-password') }}</Label>
        </div>
        <InputPassword
          id="password"
          v-model="credentials.currentPassword"
          type="password"
          required
          class="ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0 focus-visible:border-gray-500"
        />
      </div>
      <div class="grid gap-2">
        <div class="flex items-center">
          <Label for="password">{{ $t('login.new-password') }}</Label>
        </div>
        <InputPassword
          id="password"
          v-model="credentials.newPassword"
          type="password"
          required
          class="ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0 focus-visible:border-gray-500"
        />
      </div>
      <div class="grid gap-2">
        <div class="flex items-center">
          <Label for="password">{{ $t('login.repeat-new-password') }}</Label>
        </div>
        <InputPassword
          id="password"
          v-model="credentials.repeatedNewPassword"
          type="password"
          required
          class="ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0 focus-visible:border-gray-500"
        />
      </div>
      <PasswordHelper :credentials="credentials" />
      <Button type="submit" class="w-full">
        {{ $t('settings.change-password') }}
      </Button>
    </form>
    <div v-if="componentState === ComponentStateType.LOADING" class="absolute bg-white/80 w-full h-full left-0 top-0">
      <PageLoader :absolute-center="true" />
    </div>
  </div>
</template>
