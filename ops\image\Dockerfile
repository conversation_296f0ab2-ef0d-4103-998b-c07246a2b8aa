FROM node:lts-slim as install-stage

ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable

RUN mkdir -p /opt/antik/app/

# Assuming 'dist' and 'version.json' are the build artifacts from GitLab CI
COPY dist/ /opt/antik/app/dist/
COPY version.json /opt/antik/app/version.json

ARG CACHE_DATE

FROM nginx:alpine as production-stage

RUN apk add --update --no-cache nano bash

COPY --from=install-stage /opt/antik/app/dist /opt/antik/app
COPY --from=install-stage /opt/antik/app/version.json /opt/antik/app

COPY ./ops/nginx/nginx.conf /etc/nginx/nginx.conf

# .env
COPY docker-entrypoint.sh /docker-entrypoint.sh
RUN chmod +x /docker-entrypoint.sh

EXPOSE 80/tcp

ENTRYPOINT ["/docker-entrypoint.sh"]
