<script setup lang="ts">
import { IconEdit, IconPlus, IconTrash } from '@tabler/icons-vue';
import { useRouteQuery } from '@vueuse/router';
import { debounce } from 'lodash-es';
import { Search } from 'lucide-vue-next';
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import PageLoader from '@/components/global/PageLoader.vue';
import Pagination from '@/components/global/Pagination.vue';
import { routeMap } from '@/router/routes';
import { Button } from '@/shadcn-components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/shadcn-components/ui/table';
import adminApi from '@/util/adminAxios';
import { getFromMultiLangObject } from '@/util/multilang';
import { deployToast, ToastType } from '@/util/toast';
import type { Meta, Response, Service } from '@/util/types/api-responses';
import { ComponentStateType } from '@/util/types/components';

const { t } = useI18n();

const currentPage = useRouteQuery('page');
const currentLimit = useRouteQuery('limit');

const componentState = ref(ComponentStateType.LOADING);
const services = ref<Service[]>([]);
const paginationMeta = ref<Meta>();

const fetchData = async(args?: { page?: number, limit?: number, search?: string }) => {
  const to = setTimeout(() => {
    componentState.value = ComponentStateType.LOADING;
  }, 220);
  try {
    const params = {
      page: (args?.page ?? currentPage.value) ?? 1,
      limit: (args?.limit ?? currentLimit.value) ?? 15,
    } as Record<string, string | number>;
    if (args?.search && args.search.length > 2) {
      params.search = args.search;
      delete params.page;
    }

    const { data } = await adminApi.get<Response<Service[]>>('/api/admin/reservations/services', { params });

    services.value = data.data;
    paginationMeta.value = data.meta;
    clearTimeout(to);
    componentState.value = ComponentStateType.OK;
  } catch {
    clearTimeout(to);
    componentState.value = ComponentStateType.ERROR;
  }
};

await fetchData();

const onSearchInput = debounce((ev: Record<any, any>) => {
  fetchData({ search: ev.target?.value });
}, 500);

const removeService = async(id: string) => {
  try {
    await adminApi.delete(`/api/admin/reservations/services/${id}`);
    await fetchData();
    deployToast(ToastType.SUCCESS, {
      text: t('services.service-deleted'),
      timeout: 6000,
    });
  } catch {
    componentState.value = ComponentStateType.ERROR;
    deployToast(ToastType.ERROR, {
      text: t('misc.error'),
      timeout: 6000,
    });
  }
};

const hasServices = computed(() => services.value?.length > 0);
</script>

<template>
  <div class="relative">
    <div class="flex items-center justify-between mb-4">
      <h1 class="text-2xl font-bold">{{ $t('services.title') }}</h1>
      <router-link :to="{ name: routeMap.services.children.create.name }">
        <Button class="bg-green-600 hover:bg-green-700">
          <IconPlus class="w-4 h-4 mr-2" />
          {{ $t('services.new-service') }}
        </Button>
      </router-link>
    </div>

    <div class="mb-4">
      <div class="relative p-2 bg-white border-gray-300 border rounded-lg flex items-center w-64 overflow-hidden flex-1 sm:flex-none">
        <div class="pl-0.5 pr-1.5">
          <Search class="min-h-5 h-5 min-w-5 w-5 text-muted-foreground bg-mute" />
        </div>
        <input
          type="text"
          :placeholder="$t('misc.search')"
          class="w-full focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-gray-500 outline-none placeholder:text-sm"
          @input="onSearchInput"
        >
      </div>
    </div>

    <div v-if="componentState === ComponentStateType.OK" class="bg-white rounded-3xl p-6 shadow-sm">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead class="text-center">#</TableHead>
            <TableHead>{{ $t('services.name') }}</TableHead>
            <TableHead>{{ $t('services.description') }}</TableHead>
            <TableHead>{{ $t('services.capacity') }}</TableHead>
            <TableHead>{{ $t('services.duration') }}</TableHead>
            <TableHead>{{ $t('services.active') }}</TableHead>
            <TableHead class="text-right">{{ $t('user-management.actions') }}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-for="(service, index) in services" :key="service.id">
            <TableCell class="text-center">{{ index + 1 }}</TableCell>

            <TableCell v-if="service.name">
              <router-link :to="{ name: routeMap.services.children.edit.name, params: { id: service.id } }">
                <div class="hover:underline font-medium">{{ getFromMultiLangObject(service.name).value }}</div>
              </router-link>
            </TableCell>
            <TableCell v-else>-</TableCell>

            <TableCell v-if="service.description">
              <div class="text-sm text-gray-600 max-w-xs truncate">{{ getFromMultiLangObject(service.description).value }}</div>
            </TableCell>
            <TableCell v-else>-</TableCell>

            <TableCell>{{ service.capacity }}</TableCell>
            <TableCell>{{ service.duration_minutes }} min</TableCell>

            <TableCell>
              <span :class="service.active ? 'text-green-600' : 'text-red-600'" class="font-medium">
                {{ service.active ? $t('misc.yes') : 'No' }}
              </span>
            </TableCell>

            <TableCell class="text-right">
              <div class="flex gap-2 justify-end">
                <router-link :to="{ name: routeMap.services.children.edit.name, params: { id: service.id } }">
                  <Button variant="outline" size="sm">
                    <IconEdit class="w-4 h-4" />
                  </Button>
                </router-link>
                <Button variant="outline" size="sm" class="text-red-600 hover:text-red-700" @click="removeService(service.id)">
                  <IconTrash class="w-4 h-4" />
                </Button>
              </div>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>

      <Pagination v-if="hasServices" :meta="paginationMeta!" @new-page="fetchData" />

      <div v-if="!hasServices" class="text-sm text-gray-400 w-full text-center mt-4">
        {{ $t('misc.list-is-empty') }}
      </div>
    </div>
    <div v-if="componentState === ComponentStateType.LOADING" class="absolute bg-white/70 top-0 left-0 w-full h-full rounded-3xl">
      <PageLoader :fixed-center="true" />
    </div>
    <div v-else-if="componentState === ComponentStateType.ERROR" class="absolute-center bg-black text-white font-bold p-1">
      <span>{{ $t('misc.failed-to-get-data') }}!</span>
    </div>
  </div>
</template>
