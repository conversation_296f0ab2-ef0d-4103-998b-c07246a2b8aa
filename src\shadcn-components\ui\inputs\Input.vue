<script setup lang="ts">
import { computed, type HTMLAttributes } from 'vue';
import { cn } from '@/shadcn-utils';
import { useAuthStore } from '@/stores/auth-store';

const store = useAuthStore();

const emits = defineEmits(['update:modelValue', 'input']);
const props = defineProps<{
  defaultValue?: any
  multilang?: boolean
  modelValue?: any
  disabled?: boolean
  min?: string
  fit?: boolean
  placeholder?: string
  errors?: string[]
  type?: 'text' | 'number' | 'password' | 'email' | 'tel' | 'url' | 'search' | 'date' | 'time' | 'month' | 'week' | 'color'
  class?: HTMLAttributes['class']
}>();

const modelValue = computed({
  get() {
    return props.multilang ? (props.modelValue as Record<string, string>)[store.currentInputLang] || '' : props.modelValue as string;
  },
  set(newVal: string) {
    if (props.multilang) {
      const updated = { ...(props.modelValue as Record<string, string>), [store.currentInputLang]: newVal } as any;
      emits('update:modelValue', updated);
    } else {
      emits('update:modelValue', newVal);
    }
  },
});

const padding = computed(() => props.multilang ? '!pl-10' : '');
</script>

<template>
  <div :class="[fit ? '' : 'w-full', 'relative']">
    <slot name="left" />
    <button v-if="multilang" class="absolute left-2 top-1/2 -translate-y-1/2 font-medium text-white bg-paynes-gray-800 px-1 rounded select-none uppercase cursor-pointer" @click.prevent="store.nextLang">{{ store.currentInputLang }}</button>
    <input v-model="modelValue" lang="sk" :type="type ?? 'text'" :disabled :min :placeholder :class="cn(padding, 'flex h-10 w-full rounded-md border border-input bg-background px-3 shadow py-2 text-sm disabled:cursor-not-allowed disabled:opacity-50 focus:outline-paynes-gray-900', props.class, { 'focus:outline-rose-500 border-rose-500 bg-rose-50/50': errors?.length })" @input="emits('input')">
    <div v-for="error in errors" :key="error" class="text-rose-500 text-xs mt-0.5 ml-1.5">{{ error }}</div>
  </div>
</template>
