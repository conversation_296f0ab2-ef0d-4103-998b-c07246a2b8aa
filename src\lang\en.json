{"cash-register": {"active": "Active", "all-categories": "All Categories", "cash-registers": "Cash registers", "cashier": "Cashier", "change-the-cashier": "Change the cashier", "device-id": "Device ID", "groups-of-cash-registers": "Groups of cash registers", "login-title": "The cash register is locked", "new-group": "New group", "subtotal": "Subtotal", "summary": "Summary", "vat": "VAT", "total": "Total", "pay-cash": "Pay with cash", "pay-card": "Pay with card", "pay-gift-card": "Pay with voucher", "invalidEID": "The entered EID was not found", "back": "Back to Payment Methods", "payed": "Payed", "complete-payment": "Complete Payment", "edit-total": "Editing total amount", "accept-edit": "Confirm edit", "items": "Items", "split-payment": "Split payment", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "transactions": "Transactions", "transaction-history": "Transaction history", "new-deposit": "New Deposit", "new-withdrawal": "New Withdrawal", "withdrawal": "<PERSON><PERSON><PERSON>", "cash": "Cash", "voucher": "Voucher", "bills": "Bills", "unpaid-checks": "Unpaid checks", "number-of-items": "Number of items", "n-items": "{n} item | {n} items", "settings": "Cash register settings", "printer": "Printer", "payment-terminal": "Payment terminal", "associated-crs": "Associated cash registers", "discount": "Discount", "pay": "Pay", "empty-cart": "The list of items is empty", "payment-successful": "Payment Successful!", "cancel-bill": "Cnacel bill", "new-bill": "New bill", "select-payment-method": "Select Payment Method", "payment-method": "Payment Method", "payment-failed": "Payment Failed", "payment_card": "Payment Card", "payment-error": "Payment failed", "payment-timeout": "Request timed out. Please try again", "unchip": "Unchip", "unchip-message": "Please scan the wristband to remove it from the cart", "auto-logout-label": "Automatically log out after", "minutes": "minute | minutes", "product-images": "Display images for products", "report": "Report", "x-report": "X Report", "z-report": "Z Report", "apply-discount": "Apply discount", "after-discount": "After discount", "original-price": "Original price", "apply-discount-for": "Apply discount for", "customer": "Customer", "select-customer": "Select Customer", "load-from-card": "Load from Card", "payment": "Payment", "confirm-cancel": "Are you sure you want to cancel this check?", "report-failed": "Report failed", "report-timeout": "Timeout while generating report", "transaction-timeout": "Timeout while creating transaction", "rounded": "Rounded", "to-change": "Change", "change": "Change", "discount-info": "-{amount}{type} on entire order", "discount-total": "Total discounts", "cancel-discount": "Cancel discount", "surcharge": "Surcharge", "scan-ticket": "<PERSON><PERSON> Ticket", "surcharge-required": "Surcharge Required", "ticket-valid": "Ticket <PERSON>", "ticket-info": "Ticket Information", "reference": "Reference", "created-at": "Created At", "used-at": "Used At", "no-issues": "No issues found", "issue-type": "Issue Type", "extra-entries": "Extra Entries", "extra-time": "Extra Time", "total-tracked": "Total Tracked Time", "paid-time": "Paid Time", "process-surcharge": "Process Surcharge", "scan-ticket-to-start": "<PERSON>an a ticket to start", "error-fetching-ticket-detail": "Failed to load ticket detail", "error-fetching-ticket-from-reader": "Communication with reader unsuccessfull", "error-resolving-ticket": "Failed to create surcharge resolving order", "remaining-time": "Remaining time", "operations": "Operations", "refund": "Refund", "refund-order": "Refund Order", "refund-successful": "Refund Successful", "refund-failed": "Refund Failed", "order-id": "Order ID", "order-detail": "Order Detail", "search-order-uid": "Search by order UID", "scan-qr": "Scan QR Code", "quantity": "Quantity", "modal-will-close-automatically": "This modal will close automatically in 10 seconds", "error-fetching-orders": "Failed to fetch orders", "error-searching-order": "Failed to search order", "error-scanning-qr": "Failed to scan QR code", "error-fetching-order-detail": "Failed to fetch order detail", "invoice-payment": "Invoice Payment", "invoice-payment-successful": "Invoice Payment Successful", "invoice-payment-failed": "Invoice Payment Failed", "invoice-identifier": "Invoice Identifier", "enter-invoice-identifier": "Enter invoice identifier", "price": "Price", "amount-to-pay": "Amount to Pay", "received": "Received", "processing-payment": "Processing payment...", "fill-all-fields": "Please fill all required fields", "pay-invoice": "Pay on Invoice", "zero-receipt-reset": "Zero Receipt Reset", "zero-receipt-reset-successful": "Zero Receipt Reset Successful", "zero-receipt-reset-failed": "Zero Receipt Reset Failed", "zero-receipt-reset-warning": "This operation will reset the cash register with a zero receipt.", "zero-receipt-reset-description": "This function will create a zero receipt to reset the cash register state. This action cannot be undone.", "processing-reset": "Processing reset...", "perform-reset": "Perform Reset", "reset-bracelet": "Reset Bracelet", "confirm-bracelet-reset": "Confirm Bracelet Reset", "bracelet-reset-warning": "Are you sure you want to reset this bracelet? This action cannot be undone.", "bracelet-reset-successful": "Bracelet reset successful", "bracelet-reset-failed": "Bracelet reset failed"}, "form": {"delete-cr": "Are you sure you want to delete cash register {name}?", "delete-product": "Are you sure you want to delete product {name}?", "delete-customer": "Are you sure you want to delete customer {name}?", "delete-group": "Are you sure you want to delete group {name}?"}, "global": {"remove": "Remove"}, "login": {"current-password": "Current password", "errorLoggingIn": "<PERSON><PERSON> was not successful", "failed-to-reset-pw": "Error when requesting password change. Please try again later.", "forgot-password": "Forgot password", "forgot-password-desc": "After sending the form with an email, you will receive an email with a link to reset your password", "forgotten-password": "Forgotten password", "log-out": "Log out", "login": "<PERSON><PERSON>", "loginDesc": "To enter, it is necessary to authenticate using an e-mail and a password.", "min-pw-length": "Password must contain at least 8 characters", "needAccount": "Don't have an account?", "new-password": "New password", "pw-do-not-match": "Passwords do not match", "pw-helper": {"length": "Password must be at least 10 characters long.", "lowercase": "Password must contain at least one lowercase letter.", "number": "Password must contain at least one number.", "symbol": "Password must contain at least one symbol.", "uppercase": "Password must contain at least one uppercase letter."}, "pw-successfully-set": "The password has been successfully set", "reask-new-password": "New request for password change", "repeat-new-password": "Repeat new password", "reset-password": "Reset password", "reset-password-desc": "To set a new password, fill out the following form", "reset-pw-succ": "Your password was changed successfully", "signIn": "Sign in", "signUp": "Sign up"}, "logistics": {"air": "Air", "carrier": "Carrier", "currency": "<PERSON><PERSON><PERSON><PERSON>", "delete-shipment": "Are you sure you want to remove the {name} shipment?", "general-information": "General information", "item-price": "Item price", "new-shipment": "New shipment", "num-of-boxes": "Number of boxes", "price": "Price", "purchase-price": "Purchase price", "rail": "Rail", "recipient": "Recipient", "sea": "Sea", "sender": "Sender", "shipment-details": "Shipment details", "shipment-no": "Transport no. {num}", "title": "Logistics", "tracking-number": "Tracking number", "tracking-url": "Tracking URL", "type-of-carrier": "Type of carrier", "volume": "Volume", "weight": "Weight", "weight-brutto": "Weight brutto", "weight-netto": "Weight netto"}, "misc": {"add": "Add", "add-comment": "Add a comment...", "add-new": "Add new", "address": "Address", "back-to-home": "Back to homepage", "cancel": "Cancel", "choose-from-menu": "Pick an item from menu", "city": "City", "code": "Code", "continue": "Continue", "continue-prompt": "Do you want to continue?", "copy-to-clipboard": "Copy to clipboard", "create": "Create", "created": "Date created", "days": {"mo": "Monday", "tu": "Tuesday", "we": "Wednesday", "th": "Thursday", "fr": "Friday", "sa": "Saturday", "su": "Sunday"}, "dashboard": "Dashboard", "delete": "Delete", "delete-action": "Are you sure you want to delete {name}?", "description": "Description", "download": "Download", "drag-in-photo": "Choose a photo or drag it here", "edit": "Edit", "en": "English", "error": "An error occurred", "failed-to-get-data": "Failed to get data from the server", "file-upload-error": "Failed to upload file", "form-sent-successfully": "Formular was sent successfully", "hide": "<PERSON>de", "identifier": "Identifier", "list": "List", "list-is-empty": "List is empty", "loading": "Loading", "mail": "Mail", "name": "Name", "name-surname": "Name and surname", "no-results": "No results", "password": "Password", "phone": "Phone number", "photo": "Photo", "refresh": "Refresh", "remove": "Remove", "save": "Save", "search": "Search", "send": "Send", "settings": "Settings", "shortcut": "Shortcut", "show": "Show", "show-more": "Show more", "sk": "Slovak", "start": "Start", "success": "Operation was successful", "surname": "Surname", "title": "Title", "token-not-valid": "This password request has already expired", "total-br": "Total BRs", "type": "Type", "unassigned": "Not assigned", "undefined": "Undefined", "upload-new": "Upload new", "upload-photo": "Upload photo", "zh": "Chinese", "plural-handled-minutes": "minute | minutes", "plural-handled-hours": "hour | hours", "plural-handled-days": "day | days", "method": "Method", "amount": "Amount", "date": "Date", "confirm": "Confirm", "day": "Day", "end": "End", "close": "Close", "try-again": "Try Again", "zipcode": "ZIP code", "back": "Back", "status": "Status", "piece": "piece", "reset": "Reset", "warning": "Warning", "yes": "Yes"}, "orders": {"add-to-existing": "Add to existing", "add-to-existing-desc": "If no shipment is selected, a new shipment is created. Selecting a shipment will add the products to an existing shipment", "color": "Color", "create-order-status": "Create order status", "create-shipment": "Create shipment", "customer-note": "Customer's note", "documents": "Documents", "edit-order-status": "Edit order status", "estimated-length": "Estimated length", "nav-title": "Import", "new-order": "New order", "order-date": "Order Date", "order-details": "Order details", "order-reference-number": "Order reference number", "recipient": "Recipient", "reference-number": "Reference number", "shipment-date": "Shipment date", "status": "Status", "supplier-note": "Supplier's note", "title": "Orders"}, "pre-orders": {"add-custom": "Add cutom", "add-to-order": "Add to order", "back-to-products": "Back to products", "choose-different": "Choose different", "create-an-import-order": "Create an import order", "create-custom": "Create custom", "create-new-order": "Create new order", "custom-description": "Custom description", "custom-image": "Custom image", "custom-name": "Custom name", "custom-product": "Custom product", "custom-unit": "Custom unit", "deadline": "Requested delivery date", "go-to-product-card": "Go to product card", "new-pre-order": "New order", "pre-order-details": "Order details", "real_deadline": "Deadline", "required-quantity": "Required quantity", "select-from-catalogue": "Select from catalogue", "select-product-from-catalogue": "Select a product from the catalogue", "status-closed": "CLOSED", "status-in-progress": "IN PROGRESS", "status-new": "NEW", "title": "preOrders", "unassigned": "Unassigned", "upload-image": "Upload image"}, "products": {"about-product": "About the product", "active": "Active", "add-product": "Add product", "attachments": "Attachments", "catalog_number": "Catalog number", "categories": "Categories", "categories-delete-title": "Are you sure you want to delete category {name} ?", "custom-note": "Custom Note", "custom-products": "Custom products", "delete-family": "Are you sure you want to remove the {name} family?", "deposit-packaging": "Deposit packaging", "edit-attachment-type": "Edit attachment type", "edit-category": "Edit category", "edit-unit": "Edit measurement unit", "families": "Product families", "family-attach-title": "Family attachments | Family attachments", "files": "Files", "general-information": "General information", "hs-code": "HS code", "hs-code-cn": "Hs code CN", "image": "Image", "inactive": "Inactive", "last-modified": "Last modified", "min-3-search": "Enter at least 3 characters to search for a product.", "mk-number": "MK number", "model-number": "Model number", "name": "Name", "name-short": "Short name", "names-and-descriptions": "Names and descriptions", "new-attachment-type": "New attachment type", "new-category": "New category", "new-family": "New product family", "new-product": "New product", "note": "Note", "order-number": "Order number", "parent-category": "Parent Category", "price": "Price", "product": "Product", "quantity": "Quantity", "rate": "Rate", "select-categories": "Select categories", "select-parent-category": "Select the parent category", "selected": "Selected", "service": "Service", "short-description": "Short description", "show-product": "Show product", "stock-count": "Stock count", "supplier": "Supplier", "ticket": "Ticket", "title": "Products", "unit": "Unit", "ticket-settings": "Ticket settings", "time-ticket": "Time ticket", "ticket-time": "Validity period", "addition-time": "Addition time", "one-way-ticket": "One-way ticket", "scan-type": "Scanner type", "turnstile-categories": "Turnstile categories", "is-surcharge": "Surcharge ticket", "require-return": "Require return", "valid-from-time": "<PERSON>id from", "valid-to-time": "Valid to"}, "turnstiles": {"title": "Turnstiles", "categories": "Categories", "list": "List", "serial-number": "Serial number", "ip-address": "IP address", "mac-address": "MAC address", "connection": "Connection", "status": "Reader status", "config": "<PERSON><PERSON> config", "image": "Image", "used": "Used", "edit": "Edit", "save": "Save", "cancel": "Cancel", "scanner-settings": "Scanner settings", "image-settings": "Reversed", "reverse-readers": "Reverse readers", "reader-in": "Reader in", "reader-out": "Reader out", "rfid": "RFID", "mifare": "MIFARE", "qr": "QR", "zone-level": "Zone level", "name": "Name", "id": "ID", "delete-category-confirmation": "Are you sure you want to delete category {name}?", "reverse": "Reverse"}, "role-management": {"delete-role-action": "Are you sure you want to delete role {name}?", "edit-roles-failed": "Changes were not saved. Server side error.", "failed-create-role": "Failed to create role. Server side error.", "failed-delete-role": "Failed to delete role.", "role": "Role", "role-name": "Role name", "title": "Role management"}, "settings": {"change-password": "Change password", "contact-person-name": "Contact person name", "country": "Country", "create-extended-data": "Create extended data", "create-logistics-status": "Create logistics status", "create-recipient": "Create a recipient", "create-sender": "Create sender", "edit-extended-data": "Edit extended data", "edit-logistics-status": "Edit logistic status", "edit-recipient": "Edit recipient", "edit-sender": "Edit sender", "extended-data": "Extended data", "general-settings": "General settings", "logistics-settings": "Logistics settings", "logistics-statuses": "Logistics statuses", "measurement-units": "Measurement units", "new-measurement-unit": "New measurement unit", "options": "Options", "order-settings": "Order settings (Import)", "order-statuses": "Order statuses (Import)", "preorder-settings": "Order settings (internal)", "preorder-statuses": "Order statuses (internal)", "product-settings": "Product settings", "recipients": "Recipients", "senders": "Senders", "set-order-status": "Set order status", "taxesTitle": "Taxes / VAT", "types-of-attachments": "Types of attachments", "turnstiles-settings": "Turnstiles settings", "openingHours": "Active hours", "turnstile-display-text": "Display text", "turnstile-mode": "Turnstile mode", "event": "Event", "settings-saved": "Settings have been saved successfully", "turnstile-timezone": "Time zone"}, "user": {"delete-user": {"modal-desc": "This action cannot be undone. This will permanently delete the account and remove data from our servers.", "title": "Are you sure you want to delete user {name}?"}, "my-account": "My account"}, "user-management": {"actions": "Actions", "card": "Card", "create-user": "Create user", "create-user-desc": "Provide user details. When you click save, an email with activation link will be sent to user which then can choose his password.", "delete-user-action": "Are you sure you want to delete user {name}?", "edit-failed": "Changes were not saved. Server side error.", "permissions": "Permissions", "roles": "Roles", "success-delete-user": "Successfully removed user", "title": "User management", "user": "User", "user-create-fail": "An error ocurred when creating new user", "user-create-success": "New user has been created", "user-delete-fail": "Failed to delete user"}, "customers": {"title": "Customers", "groups": "Groups", "new-customer": "New Customer", "can_invoice_order": "Can invoice order", "customer_groups": "Customer groups", "customer-settings": "Customer settings", "customer-cards": "Custmoer cards", "add-card": "Add Card", "card-number": "Card number", "card-type": "Card type", "new-group": "New group"}, "services": {"title": "Services", "list": "List", "name": "Name", "description": "Description", "capacity": "Capacity", "duration": "Duration (minutes)", "slot-time-duration": "Slot time duration (minutes)", "active": "Active", "use-resources": "Use resources", "use-variants": "Use variants", "resources": "Resources", "variants": "Variants", "general-information": "General information", "resource-settings": "Resource settings", "variant-settings": "Variant settings", "add-resource": "Add resource", "add-variant": "Add variant", "resource-name": "Resource name", "variant-name": "Variant name", "variant-duration": "Variant duration (minutes)", "only-resources": "Only for resources", "new-service": "New service", "edit-service": "Edit service", "delete-service-confirmation": "Are you sure you want to delete service {name}?", "service-created": "Service created successfully", "service-updated": "Service updated successfully", "service-deleted": "Service deleted successfully"}}