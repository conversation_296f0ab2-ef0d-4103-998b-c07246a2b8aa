<script lang="ts" setup>
import { ChevronLeft } from 'lucide-vue-next';
import { useRouter } from 'vue-router';
import ForgotPasswordForm from '@/pages/forgot-password/ForgotPasswordForm.vue';
import { routeMap } from '@/router/routes';

const router = useRouter();
</script>

<template>
  <div class="w-screen h-dvh flex items-center justify-center">
    <div class="w-full h-full flex justify-center items-center lg:items-stretch lg:grid lg:grid-cols-2">
      <router-link :to="{name: routeMap.home.name}" class="hidden bg-muted lg:flex items-center justify-center">
        <div class="h-96 w-auto">
          <img src="@/assets/images/apm-logo.png" alt="" class="w-[400px]">
        </div>
      </router-link>
      <div class="flex items-center justify-center py-12">
        <ForgotPasswordForm />
      </div>
    </div>
    <div class="cursor-pointer hover:bg-gray-400 active:bg-gray-400 absolute top-6 right-6 sm:top-4 sm:right-4 rounded-full bg-gray-300 w-10 h-10 flex items-center justify-center" @click="router.back()">
      <ChevronLeft class="relative right-px text-gray-700" />
    </div>
  </div>
</template>
