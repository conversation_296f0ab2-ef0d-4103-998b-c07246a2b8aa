import { defineStore } from 'pinia';
import { ref } from 'vue';
import cashApi, { setFrontendIdHeader } from '@/util/cashAxios';
import type { CRType, CR, CRLayout, CROrder, CommandResponse } from '@/util/types/api-responses';

export const useCashRegisterStore = defineStore('cash-register', () => {
  const layout = ref<CRLayout>();
  const crID = ref<string>();
  const feID = ref<string>();
  const type = ref<CRType>();
  const order = ref<CROrder>();
  const paymentDoneID = ref<string>();
  const dataFromReader = ref<string>();
  const unchipTicketID = ref<string>();
  const imageInProducts = ref(true);
  const commandResponse = ref<CommandResponse>();

  const init = (actualDisplay: number, data: CR) => {
    const fe = data.frontends.find(f => f.display === actualDisplay.toString(10))!;
    crID.value = data.id;
    feID.value = fe.id;
    type.value = fe.type;
    layout.value = data.layout;
    setFrontendIdHeader(feID.value);
  };

  const loadCart = async(id?: string) => {
    const { data: { data }} = await cashApi.get(`/api/cash-register/orders/${id ?? order.value?.id}`, {
      params: {
        notify_frontends: true,
      },
    });

    order.value = data;
  };

  const removeCustomerFromOrder = async() => {
    if (!order.value?.id) {
      return;
    }
    try {
      const { data: { data }} = await cashApi.put(`/api/cash-register/orders/${order.value.id}`, { customer_id: null });
      order.value = data;
      await loadCart();
    } catch (error) {
      console.error('Error removing customer:', error);
    }
  };

  const clearCart = () => {
    order.value = undefined;
    paymentDoneID.value = undefined;
  };

  return {
    layout,
    feID,
    crID,
    type,
    order,
    paymentDoneID,
    dataFromReader,
    unchipTicketID,
    imageInProducts,
    commandResponse,
    init,
    loadCart,
    clearCart,
    removeCustomerFromOrder,
  };
});
