<script lang="ts" setup>
import { IconCashRegister } from '@tabler/icons-vue';
import { ChevronRight } from 'lucide-vue-next';
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { filterNavLinks } from '@/router/navbar';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/shadcn-components/ui/collapsible';
import { Sidebar, SidebarContent, SidebarGroup, SidebarGroupContent, SidebarGroupLabel, SidebarMenu, SidebarMenuButton, SidebarMenuItem, SidebarMenuSub, SidebarFooter, SidebarMenuSubButton, SidebarHeader } from '@/shadcn-components/ui/sidebar';
import { useAuthStore } from '@/stores/auth-store';

const emit = defineEmits(['openSidebar', 'close-sidebar']);

const authStore = useAuthStore();
const route = useRoute();
const router = useRouter();

const filteredNavLinks = filterNavLinks(authStore.user!.permissions);

const map: Record<number, boolean | undefined> = {};
const activeMap = ref<Record<number, boolean | undefined>>({});

filteredNavLinks.top.forEach((link, i) => {
  map[i] = link.children?.some(n => n.activeIn.includes(route.name as never));
});

activeMap.value = map;

const navigate = (name: string, setOpenMobile?: (arg: boolean) => void) => {
  if (setOpenMobile) {
    setOpenMobile(false);
  }

  for (const [index, link] of filteredNavLinks.top.entries()) {
    const hasActiveChild = link.children?.some(child => child.activeIn.includes(name as never));
    activeMap.value[index] = !!hasActiveChild;
  }

  router.push({ name });
  emit('close-sidebar');
};

const neviemJakToMamNazvat = (index: number) => {
  emit('openSidebar');
  activeMap.value[index] = true;
};
</script>

<template>
  <Sidebar ref="side" collapsible="icon">
    <template #default="neviem">
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem tooltip="asdasd" size="lg" class="fig">
            <div class="size-[30px]">
              <IconCashRegister size="30" class="text-paynes-gray-600" />
            </div>
            <SidebarGroupLabel class="text-blacks text-sm -translate-x-2">Cash Registers</SidebarGroupLabel>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              <template v-for="(link, index) in filteredNavLinks.top" :key="index">
                <Collapsible v-if="link.children" v-model:open="activeMap[index]">
                  <template #default="{ open }">
                    <SidebarMenuItem>
                      <CollapsibleTrigger as-child>
                        <SidebarMenuButton class="w-full" :tooltip="$t(link.i18nTitle)" @click="neviemJakToMamNazvat(index)">
                          <component :is="link.icon" />
                          {{ $t(link.i18nTitle) }}
                          <ChevronRight :class="[open ? 'rotate-90': '', 'ml-auto transition-transform']" />
                        </SidebarMenuButton>
                      </CollapsibleTrigger>
                      <CollapsibleContent>
                        <SidebarMenuSub>
                          <SidebarMenuSubButton
                            v-for="(child, childIndex) in link.children"
                            :key="childIndex"
                            :class="[child.activeIn.includes($route.name as never) ? '!text-paynes-gray-700' : '']"
                            :tooltip="$t(child.i18nTitle)"
                            @click="navigate(child.name, neviem?.setOpenMobile)"
                          >
                            <span>{{ $t(child.i18nTitle) }}</span>
                          </SidebarMenuSubButton>
                        </SidebarMenuSub>
                      </CollapsibleContent>
                    </SidebarMenuItem>
                  </template>
                </Collapsible>

                <SidebarMenuItem v-else>
                  <SidebarMenuButton
                    :icon="link.icon"
                    :class="[link.activeIn.includes($route.name as never) ? '!text-paynes-gray-700' : '']"
                    :tooltip="$t(link.i18nTitle)"
                    @click="navigate(link.name, neviem?.setOpenMobile)"
                  >
                    <component :is="link.icon" />
                    {{ $t(link.i18nTitle) }}
                  </SidebarMenuButton>
                </SidebarMenuItem>
              </template>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter>
        <SidebarGroupLabel>Admin</SidebarGroupLabel>
        <SidebarMenu>
          <SidebarMenuItem v-for="(link, index) in filteredNavLinks.bottom" :key="index">
            <SidebarMenuButton
              :icon="link.icon"
              :class="[link.name === ($route.name as never) ? '!text-paynes-gray-700' : '']"
              :tooltip="$t(link.i18nTitle)"
              @click="navigate(link.name, neviem?.setOpenMobile)"
            >
              <component :is="link.icon" />
              <span>{{ $t(link.i18nTitle) }}</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </template>
  </Sidebar>
</template>
