<script setup lang="ts">
import { IconDots, IconPercentage, IconShoppingCart, IconTag, IconTicket, IconUser, IconWallet } from '@tabler/icons-vue';
import { computed, nextTick, ref, watch } from 'vue';
import DiscountModal from '@/pages/cash-register/dialogs/DiscountModal.vue';
import ListEditProductModal from '@/pages/cash-register/dialogs/ListEditProductModal.vue';
import PaymentModal from '@/pages/cash-register/dialogs/PaymentModal.vue';
import { useCashRegisterStore } from '@/stores/cash-register';
import cashApi from '@/util/cashAxios';
import { getFromMultiLangObject } from '@/util/multilang';
import type { CROrderItem } from '@/util/types/api-responses';

const store = useCashRegisterStore();

const listRef = ref<HTMLElement>();
const productForDiscount = ref<CROrderItem>();
const editModalVisible = ref(false);
const discountModalVisible = ref(false);
const paymentModalVisible = ref(false);
const editProductData = ref<CROrderItem>();
const selectedTickets = ref<string[]>([]);

const selectForScan = ({ id, product: { ticket }}: CROrderItem) => {
  if (selectedTickets.value.includes(id)) {
    selectedTickets.value = selectedTickets.value.filter(ticketId => ticketId !== id);
  } else if (ticket) {
    selectedTickets.value.push(id);
  }
};

const showEditModal = (product: CROrderItem) => {
  editProductData.value = product;
  editModalVisible.value = true;
};

const fakePipnutie = () => {
  if (import.meta.env.DEV) {
    window.receiveResponse(`{"action":"dataFromReader","data":"${Math.random().toString(36).slice(2)}","inputDevice":{"name":"StrongLink USB CardReader","id":6,"vendorId":1241,"productId":5379,"descriptor":"ecb58b2358483198f0a3c079c9e3b4c60760f2a7","keyboardType":2,"sources":"-2147483391 (KEYBOARD)","controllerNumber":0,"isVirtual":false,"hasMicrophone":false}}`);
    // window.receiveResponse(`{"action":"dataFromReader","data":"bhqohek6bdu","inputDevice":{"name":"StrongLink USB CardReader","id":6,"vendorId":1241,"productId":5379,"descriptor":"ecb58b2358483198f0a3c079c9e3b4c60760f2a7","keyboardType":2,"sources":"-2147483391 (KEYBOARD)","controllerNumber":0,"isVirtual":false,"hasMicrophone":false}}`);
  }
};

const openDiscountModal = (product: CROrderItem) => {
  discountModalVisible.value = true;
  editModalVisible.value = false;
  productForDiscount.value = product;
};

watch(() => store.dataFromReader, async(ticket_reference) => {
  await Promise.allSettled(selectedTickets.value.map(id => cashApi.post(`/api/cash-register/orders/items/${id}/ticket`, {
    ticket_reference,
  })));

  store.loadCart();
  store.dataFromReader = undefined;
});

watch(() => store.order?.items?.length, () => {
  nextTick(() => {
    if (listRef.value) {
      listRef.value.scrollTop = listRef.value.scrollHeight;
    }
  });
});

const allScanned = computed(() => store.order?.items.filter(item => item.product.ticket).every(item => item.tickets_count === item.quantity));
</script>

<template>
  <div class="flex flex-col border-r border-gray-300">
    <header class="fig m-2" @click="fakePipnutie">
      <IconShoppingCart size="32" />
      <h1 class="text-2xl font-bold">{{ $t('cash-register.items') }} ({{ store.order?.items?.length ?? 0 }})</h1>
    </header>
    <div ref="listRef" class="flex-1 p-3 gap-2 overflow-y-scroll">
      <div v-if="store.order?.items?.length" class="grid gap-2.5">
        <div v-for="item in store.order?.items" :key="item.id" :class="[selectedTickets.includes(item.id) && '!bg-cyan-100 !border-cyan-400', 'grid grid-cols-[75%_1fr] bg-white border border-gray-300 rounded-lg p-2.5']" @click.self="selectForScan(item)">
          <div class="font-medium pointer-events-none space-y-3">
            <div class="flex items-center gap-2 text-xl line-clamp-2 overflow-visible h-[18px]">
              <IconTicket v-if="item.product.ticket" class="text-cyan-400" />
              <div class="line-clamp-1 w-full">{{ getFromMultiLangObject(item.product.name) }}</div>
            </div>
            <div class="text-lg leading-4 text-black/50 fig gap-1.5">
              <span>{{ item.quantity }}x</span>
              <span>&bullet;</span>
              <div v-if="item.discount_amount" class="fig gap-1.5">
                <span class="line-through">{{ item.price_unit.toFixed(2) }} €</span>
                <span class="text-emerald-500">{{ (item.price_calculated / item.quantity).toFixed(2) }} €</span>
              </div>
              <span v-else>{{ item.price_unit.toFixed(2) }} €</span>
            </div>
          </div>

          <div class="ml-auto h-full flex flex-col justify-between">
            <div class="h-[10px] w-[30px] ml-auto mr-2 mt-1 flex items-center text-blue-500" @click="showEditModal(item)">
              <IconDots size="30" />
            </div>
            <div v-if="item.discount_amount" :class="[item.discount_calculated && 'text-emerald-500', 'font-bold text-2xl leading-5']">
              {{ (item.price_calculated).toFixed(2) }} €
            </div>
            <div v-else class="font-bold text-2xl leading-5">{{ (item.price).toFixed(2) }} €</div>
          </div>

          <div v-if="item.product.ticket" class="col-span-2 mt-1.5">
            <div class="bg-gray-300 h-2 relative rounded-full overflow-hidden w-full">
              <div :style="{ width: `${(item.tickets_count! / item.quantity) * 100}%` }" class="bg-emerald-500 h-full absolute bottom-0 left-0" />
            </div>
          </div>
        </div>
      </div>
      <div v-else class="grid place-content-center h-full text-lg text-gray-400 italic">
        {{ $t('cash-register.empty-cart') }}
      </div>
    </div>

    <div class="bg-emerald-600/70 h-px" />
    <div v-if="store.order?.discount_amount" class="font-bold flex items-center justify-between px-3 mt-3">
      <div>
        <div class="text-2xl">Celkom:</div>
        <div class="text-emerald-500 flex items-center gap-1 text-lg">
          <IconTag size="20" stroke-width="2.5" />
          <div>{{ $t('cash-register.discount-info', { amount: store.order?.discount_amount, type: store.order?.discount_type === 'PERCENT' ? '%' : '€' }) }}</div>
        </div>
      </div>
      <div class="flex flex-col items-end">
        <div class="text-xl -mb-1 line-through text-black/55">{{ store.order?.items?.reduce((acc, item) => acc + item.price_calculated, 0).toFixed(2) ?? '0.00' }} €</div>
        <div class="text-3xl text-emerald-500">{{ store.order?.price_calculated.toFixed(2) ?? '0.00' }} €</div>
      </div>
    </div>
    <div v-else class="font-bold flex items-center justify-between px-3 mt-3">
      <div class="text-2xl">Celkom:</div>
      <div class="text-3xl">{{ store.order?.price_calculated.toFixed(2) ?? '0.00' }} €</div>
    </div>

    <!-- Customer Info -->
    <div v-if="store.order?.customer" class="bg-violet-50 border border-violet-200 rounded-lg mx-3 mt-3 p-1">
      <div class="flex items-center gap-2 text-violet-700">
        <IconUser class="h-5 w-5" />
        <div class="font-medium">{{ store.order.customer.name }}</div>
        <div class="flex items-center ml-auto gap-2">
          <div v-if="store.order.customer.discount_percent_calculated > 0" class="bg-green-100 border border-green-300 text-green-800 px-2 py-1 rounded-md text-sm">
            -{{ store.order.customer.discount_percent_calculated }}%
          </div>
          <button class="w-fit text-nowrap bg-orange-400 text-white px-2 py-1 text-sm rounded-md border border-transparent" @click="store.removeCustomerFromOrder">
            {{ $t('misc.cancel') }}
          </button>
        </div>
      </div>
    </div>

    <div class="fig gap-2.5 mt-3 pt-0 p-3">
      <button :disabled="!store.order?.items?.length" class="bg-orange-400 disabled:opacity-50 text-white w-1/3 p-5 rounded-lg text-2xl font-medium fig justify-center" @click="discountModalVisible = true">
        <IconPercentage />
        <span>
          {{ $t('cash-register.discount') }}
        </span>
      </button>
      <button :disabled="!store.order?.items?.length || !allScanned" class="bg-emerald-500 disabled:opacity-50 text-white w-2/3 p-5 rounded-lg text-2xl font-medium fig justify-center" @click="paymentModalVisible = true">
        <IconWallet />
        <span>
          {{ $t('cash-register.pay') }}
        </span>
      </button>
    </div>

    <ListEditProductModal v-if="editModalVisible" :product="editProductData!" @close="editModalVisible = false" @open-discount-modal="openDiscountModal" />
    <PaymentModal v-if="paymentModalVisible" @close="paymentModalVisible = false" />
    <DiscountModal v-if="discountModalVisible" :product="productForDiscount!" @close="discountModalVisible = false; productForDiscount = undefined" />
  </div>
</template>
