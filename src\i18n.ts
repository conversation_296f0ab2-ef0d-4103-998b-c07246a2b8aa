import { createI18n } from 'vue-i18n';
import messages from '@/lang/messages';

const pluralRules = {
  sk: (choice: number) => (choice === 0 ? 2 : (choice === 1 ? 0 : (choice <= 4 ? 1 : 2))),
  en: (choice: number) => (choice === 1 ? 0 : 1),
};

const supportedLocales = ['sk', 'en'];
const storedLang = localStorage.getItem('appLang');
const browserLang = navigator.languages?.[0]?.slice(0, 2);

const selectedLocale = storedLang ?? (supportedLocales.includes(browserLang) ? browserLang : 'sk');

const i18n = createI18n({
  legacy: false,
  locale: selectedLocale,
  fallbackLocale: 'sk',
  messages,
  globalInjection: true,
  pluralizationRules: pluralRules,
  pluralRules,
});

export default i18n;
