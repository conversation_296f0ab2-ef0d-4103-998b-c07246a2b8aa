<script setup lang="ts">
import { IconLoader2, IconCircleCheck, IconExclamationCircle, IconCash, IconCreditCardPay, IconGiftCard } from '@tabler/icons-vue';
import { ref, computed, onUnmounted, onBeforeUnmount } from 'vue';
import { useI18n } from 'vue-i18n';
import Keypad from '@/pages/cash-register/dialogs/components/Keypad.vue';
import cashApi from '@/util/cashAxios';
import { CustomEvents } from '@/util/facades/custom-event';
import { deployToast, ToastType } from '@/util/toast';

type PaymentMethod = 'CASH' | 'PAYMENT_CARD' | 'VOUCHER';

interface Payment {
  method: PaymentMethod;
  amount: number;
}

const { t } = useI18n();
const emit = defineEmits(['close']);

const invoiceIdentifier = ref('');
const price = ref<string[]>([]);
const payments = ref<Payment[]>([]);
const paymentMethod = ref<PaymentMethod>();
const payed = ref<string[]>([]);
const loading = ref(false);
const paymentResult = ref<'PENDING' | 'SUCCESS' | 'ERROR'>('PENDING');
const errorMessage = ref('');
const commandId = ref<string>();
const timeout = ref<ReturnType<typeof setTimeout>>();

const priceFormatted = computed(() =>
  price.value.join('').replace(/^0+(?!\.)/, ''),
);
const payedFormatted = computed(() => payed.value.join(''));
const remaining = ref<number>(0);
const change = computed(() => Number(payedFormatted.value) - (remaining.value || Number(priceFormatted.value)));

const createOnRegisterCommandHandler = (resolve: (value?: any | PromiseLike<any>) => void, reject: (reason: string) => void) => {
  return (e: Event) => {
    const { detail } = e as CustomEvent<{ action: string, id: string, data: {state: string} }>;

    if (detail.action !== 'cash_register_command_response' || detail.id !== commandId.value) {
      return;
    }

    if (detail.data.state !== 'OK') {
      reject('State not OK');
      return;
    }

    resolve();
  };
};

const payInvoice = async() => {
  if (!invoiceIdentifier.value.trim() || !priceFormatted.value || payments.value.length === 0) {
    deployToast(ToastType.ERROR, {
      text: t('cash-register.fill-all-fields'),
      timeout: 6000,
    });
    return;
  }

  loading.value = true;
  paymentResult.value = 'PENDING';

  try {
    const { promise: paymentPromise, resolve, reject } = Promise.withResolvers<any | undefined>();
    const handler = createOnRegisterCommandHandler(resolve, reject);
    window.eventBus.addEventListener(CustomEvents.CashRegisterCommandResponse, handler);
    const timeoutId = setTimeout(() => {
      reject('Timeout');
    }, 20000);
    onBeforeUnmount(() => {
      window.eventBus.removeEventListener(CustomEvents.CashRegisterCommandResponse, handler);
      clearTimeout(timeoutId);
    });

    const { data } = await cashApi.post<{cash_register_command_id: string}>('/api/cash-register/invoices/payment', {
      invoice_identifier: invoiceIdentifier.value,
      price: Number(priceFormatted.value),
      payments: payments.value,
    });
    commandId.value = data.cash_register_command_id;

    await paymentPromise;

    clearTimeout(timeoutId);
    window.eventBus.removeEventListener(CustomEvents.DataFromReaderEvent, handler);

    paymentResult.value = 'SUCCESS';

    deployToast(ToastType.SUCCESS, {
      text: t('cash-register.invoice-payment-successful'),
      timeout: 6000,
    });
    setTimeout(() => {
      emit('close');
    }, 10000);
  } catch (error: any) {
    loading.value = false;
    paymentResult.value = 'ERROR';
    errorMessage.value = error.response?.data?.message || t('cash-register.invoice-payment-failed');
    if (timeout.value) {
      clearTimeout(timeout.value);
    }
  }
};

const payByCash = () => {
  payments.value.push({
    method: paymentMethod.value!,
    amount: Number(payedFormatted.value),
  });

  payInvoice();
};

const payByCard = () => {
  paymentMethod.value = 'PAYMENT_CARD';
  payments.value.push({
    method: 'PAYMENT_CARD',
    amount: payments.value.length ? remaining.value : Number(priceFormatted.value),
  });

  payInvoice();
};

const splitPayment = () => {
  remaining.value = (remaining.value || Number(priceFormatted.value)) - Number(payedFormatted.value);

  payments.value.push({
    method: paymentMethod.value!,
    amount: Number(payedFormatted.value),
  });

  paymentMethod.value = undefined;
  payed.value = [];
};

const close = () => {
  if (!loading.value) {
    emit('close');
  }
};

const tryAgain = () => {
  loading.value = false;
  paymentMethod.value = undefined;
  payments.value = [];
  paymentResult.value = 'PENDING';
  commandId.value = undefined;
  errorMessage.value = '';
};

const reset = () => {
  invoiceIdentifier.value = '';
  price.value = [];
  payments.value = [];
  paymentMethod.value = undefined;
  payed.value = [];
  remaining.value = 0;
  paymentResult.value = 'PENDING';
  errorMessage.value = '';
  commandId.value = undefined;
};

onUnmounted(() => {
  if (timeout.value) {
    clearTimeout(timeout.value);
  }
});
</script>

<template>
  <div class="fixed inset-0 z-50 bg-black/65 grid place-items-center font-medium" @click.self="close">
    <div class="max-w-[520px] w-full bg-white p-12 rounded-xl text-xl max-h-[90vh] overflow-y-auto">
      <h2 class="text-2xl font-bold text-center mb-6">{{ $t('cash-register.invoice-payment') }}</h2>

      <!-- Success State -->
      <div v-if="paymentResult === 'SUCCESS'" class="text-center space-y-4">
        <IconCircleCheck class="mx-auto h-16 w-16 text-green-500" />
        <h3 class="text-xl font-bold text-green-600">{{ $t('cash-register.invoice-payment-successful') }}</h3>
        <p class="text-gray-600">{{ $t('cash-register.modal-will-close-automatically') }}</p>
        <button class="w-full bg-green-500 py-4 font-medium text-white rounded-xl" @click="emit('close')">
          {{ $t('misc.close') }}
        </button>
      </div>

      <!-- Error State -->
      <div v-else-if="paymentResult === 'ERROR'" class="text-center space-y-4">
        <IconExclamationCircle class="mx-auto h-16 w-16 text-red-500" />
        <h3 class="text-xl font-bold text-red-600">{{ $t('cash-register.invoice-payment-failed') }}</h3>
        <p class="text-gray-600">{{ errorMessage }}</p>
        <div class="flex gap-4">
          <button class="flex-1 bg-gray-500 py-4 font-medium text-white rounded-xl" @click="emit('close')">
            {{ $t('misc.close') }}
          </button>
          <button class="flex-1 bg-blue-500 py-4 font-medium text-white rounded-xl" @click="tryAgain">
            {{ $t('misc.try-again') }}
          </button>
        </div>
      </div>

      <!-- Form State -->
      <div v-else class="space-y-6">
        <!-- Invoice Identifier Input -->
        <div v-if="!paymentMethod">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            {{ $t('cash-register.invoice-identifier') }}
          </label>
          <input
            v-model="invoiceIdentifier"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            :placeholder="$t('cash-register.enter-invoice-identifier')"
          >
        </div>

        <!-- Price Input -->
        <div v-if="!paymentMethod">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            {{ $t('cash-register.price') }}
          </label>
          <div class="text-center mb-4">
            <div class="text-3xl font-bold">{{ priceFormatted || '0' }} €</div>
          </div>
          <Keypad v-model="price" with-dot />
        </div>

        <!-- Payment Method Selection -->
        <div v-if="invoiceIdentifier && priceFormatted && !paymentMethod && !loading">
          <h3 class="text-lg font-semibold mb-4">{{ $t('cash-register.select-payment-method') }}</h3>
          <div class="space-y-3">
            <button
              v-if="!payments.some(payment => payment.method === 'CASH')"
              class="flex items-center justify-center w-full bg-green-500 py-4 gap-4 font-medium text-white rounded-xl hover:bg-green-600 transition-colors"
              @click="paymentMethod = 'CASH'"
            >
              <IconCash />
              <span>{{ $t('cash-register.pay-cash') }}</span>
            </button>
            <button
              class="flex items-center justify-center w-full bg-blue-500 py-4 gap-4 font-medium text-white rounded-xl hover:bg-blue-600 transition-colors"
              @click="payByCard"
            >
              <IconCreditCardPay />
              <span>{{ $t('cash-register.pay-card') }}</span>
            </button>
            <button
              v-if="!payments.some(payment => payment.method === 'VOUCHER')"
              class="flex items-center justify-center w-full bg-amber-500 py-4 gap-4 font-medium text-white rounded-xl hover:bg-amber-600 transition-colors"
              @click="paymentMethod = 'VOUCHER'"
            >
              <IconGiftCard />
              <span>{{ $t('cash-register.pay-gift-card') }}</span>
            </button>
          </div>
        </div>

        <div v-if="paymentMethod" class="space-y-4 w-full">
          <div class="text-center flex flex-col gap-1 w-full">
            <div class="flex items-center gap-2 justify-between">
              <div class="text-lg text-gray-600">{{ $t('cash-register.invoice-identifier') }}:</div>
              <div class="text-xl font-bold">{{ invoiceIdentifier }}</div>
            </div>
            <div class="flex items-center gap-2 justify-between">
              <div class="text-lg text-gray-600">{{ $t('cash-register.amount-to-pay') }}:</div>
              <div class="text-xl font-bold">{{ remaining || priceFormatted }} €</div>
            </div>
          </div>
        </div>

        <!-- Cash Payment Input -->
        <div v-if="paymentMethod === 'CASH' && !loading" class="space-y-4">
          <div class="text-center">
            <div class="text-lg text-gray-600">{{ $t('cash-register.received') }}</div>
            <div class="text-3xl font-bold">{{ payedFormatted || '0' }} €</div>
            <div v-if="change >= 0" class="text-lg text-emerald-600">
              {{ $t('cash-register.change') }}: {{ change.toFixed(2) }} €
            </div>
          </div>

          <Keypad v-model="payed" with-dot />

          <button
            v-if="change >= 0"
            :disabled="loading"
            class="w-full bg-emerald-500 py-5 font-medium text-white rounded-xl flex justify-center items-center gap-2 hover:bg-emerald-600 transition-colors"
            @click="payByCash"
          >
            <IconLoader2 v-if="loading" class="animate-spin" />
            <span>{{ $t('cash-register.complete-payment') }}</span>
          </button>
          <button
            v-else-if="Number(payed.join('')) > 0"
            class="w-full bg-blue-500 py-5 font-medium text-white rounded-xl hover:bg-blue-600 transition-colors"
            @click="splitPayment"
          >
            <span>{{ $t('cash-register.split-payment') }}</span>
          </button>
          <button v-else class="w-full bg-neutral-400 py-5 font-medium text-white rounded-xl cursor-not-allowed">
            <span>{{ $t('cash-register.complete-payment') }}</span>
          </button>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="text-center py-8">
          <IconLoader2 class="animate-spin h-12 w-12 mx-auto text-blue-500 mb-4" />
          <p class="text-gray-600">{{ $t('cash-register.processing-payment') }}</p>
        </div>

        <!-- Reset Button -->
        <div v-if="!loading && (invoiceIdentifier || priceFormatted || payments.length)" class="pt-4 border-t">
          <button
            class="w-full bg-gray-500 py-3 font-medium text-white rounded-xl hover:bg-gray-600 transition-colors"
            @click="reset"
          >
            {{ $t('misc.reset') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
