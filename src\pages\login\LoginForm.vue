<script setup lang="ts">
import { reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';
import EnIcon from '@/assets/svg/en-flag.svg';
import SkIcon from '@/assets/svg/sk-flag.svg';
import PageLoader from '@/components/global/PageLoader.vue';
import { routeMap } from '@/router/routes';
import { Button } from '@/shadcn-components/ui/button';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from '@/shadcn-components/ui/dropdown-menu';
import { InputPassword } from '@/shadcn-components/ui/input-password';
import Input from '@/shadcn-components/ui/inputs/Input.vue';
import { Label } from '@/shadcn-components/ui/label';
import { useAuthStore } from '@/stores/auth-store';
import { deployToast, ToastType } from '@/util/toast';
import { ComponentStateType } from '@/util/types/components';

const { locale } = useI18n();
const { login } = useAuthStore();
const { t } = useI18n();
const componentState = ref(ComponentStateType.OK);
const route = useRoute();
const router = useRouter();

const flagMap = {
  sk: SkIcon,
  en: EnIcon,
};

const loginCredentials = reactive({
  login: '',
  password: '',
});

const changeLang = (lang: 'sk' | 'en') => {
  localStorage.setItem('appLang', lang);
  locale.value = lang;
};

const onLoginRequest = async() => {
  componentState.value = ComponentStateType.LOADING;
  try {
    await login(loginCredentials.login, loginCredentials.password);
    const queryTmp = { ...route.query };
    delete queryTmp.redirect;
    await router.replace({ query: queryTmp, path: route.query.redirect as string ?? '/' });
  } catch {
    deployToast(ToastType.ERROR, {
      text: t('login.errorLoggingIn'),
      timeout: 6000,
    });
  }
  componentState.value = ComponentStateType.OK;
};

</script>

<template>
  <div class="mx-auto w-[18rem] lw:w-[22rem]">
    <div v-if="componentState === ComponentStateType.OK" class="w-full grid gap-6">
      <div class="lg:hidden w-1/2 h-auto mx-auto mb-4">
        APM
      </div>
      <div class="grid gap-2 text-center">
        <h1 class="text-3xl font-bold">
          {{ $t('login.login') }}
        </h1>
        <p class="text-sm text-balance text-muted-foreground">
          {{ $t('login.loginDesc') }}
        </p>
      </div>
      <form class="grid gap-4" @submit.prevent="onLoginRequest">
        <div class="grid gap-2">
          <div class="fig justify-between">
            <Label for="email">{{ $t('misc.mail') }}</Label>
            <DropdownMenu>
              <DropdownMenuTrigger class="text-sm hover:underline fig gap-0">
                <component :is="flagMap[locale]" class="scale-[.65]" />
                {{ $t(`misc.${locale}`) }}
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem class="fig gap-1 cursor-pointer" @click="changeLang('sk')">
                  <SkIcon class="scale-75" />
                  <span>{{ $t('misc.sk') }}</span>
                </DropdownMenuItem>
                <DropdownMenuItem class="fig gap-1 cursor-pointer" @click="changeLang('en')">
                  <EnIcon class="scale-75" />
                  <span>{{ $t('misc.en') }}</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <Input
            v-model="loginCredentials.login"
            type="email"
            placeholder="<EMAIL>"
            required
            class="ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0 focus-visible:border-gray-500"
          />
        </div>
        <div class="grid gap-2">
          <div class="flex items-center">
            <Label for="password">{{ $t('misc.password') }}</Label>
            <router-link :to="{name: routeMap.forgotPassword.name}" class="ml-auto text-xs underline block" tabindex="-1">
              {{ $t('login.forgot-password') }}
            </router-link>
          </div>
          <InputPassword
            id="password"
            v-model="loginCredentials.password"
            type="password"
            required
            class="ring-offset-0 focus-visible:ring-offset-0 focus-visible:ring-0 focus-visible:border-gray-500"
          />
        </div>
        <Button type="submit" class="w-full">
          {{ $t('login.signIn') }}
        </Button>
      </form>
    </div>
    <div v-else-if="componentState === ComponentStateType.LOADING">
      <PageLoader :flex-center="true" />
    </div>
  </div>
</template>
