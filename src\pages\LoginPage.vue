<script lang="ts" setup>
import { IconCashRegister } from '@tabler/icons-vue';
import AntikSvg from '@/assets/svg/antik_logo.svg';
import LoginForm from '@/pages/login/LoginForm.vue';
</script>

<template>
  <div class="w-screen h-dvh flex items-center justify-center">
    <div class="w-full h-full flex justify-center items-center lg:items-stretch lg:grid lg:grid-cols-2">
      <div class="hidden bg-muted lg:flex items-center justify-center">
        <div class="flex flex-col gap-3 items-center">
          <IconCashRegister size="110" />
          <AntikSvg class="w-52" />
        </div>
      </div>
      <div class="flex items-center justify-center py-12">
        <LoginForm />
      </div>
    </div>
  </div>
</template>
