@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --ring: 212.7 26.8% 83.9%;

    --sidebar-background: 240 5.9% 10%;

    --sidebar-foreground: 240 4.8% 95.9%;

    --sidebar-primary: 224.3 76.3% 48%;

    --sidebar-primary-foreground: 0 0% 100%;

    --sidebar-accent: 240 3.7% 15.9%;

    --sidebar-accent-foreground: 240 4.8% 95.9%;

    --sidebar-border: 240 3.7% 15.9%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-white text-foreground;
  }

  body {
    padding: 0!important;
  }
}

@layer base {

  .custom-scrollbar-1 {
    --sb-track-color: #ffffff;
    --sb-thumb-color: #2e4d58;
    --sb-size: 5px;

    &::-webkit-scrollbar {
      width: var(--sb-size)
    }

    &::-webkit-scrollbar-track {
      background: var(--sb-track-color);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--sb-thumb-color);
      border-radius: 3px;

    }

    @supports not selector(::-webkit-scrollbar) {
      & {
        scrollbar-color: var(--sb-thumb-color)
        var(--sb-track-color);
      }
    }
  }
}

@layer base {
  .absolute-center {
    @apply absolute -translate-x-1/2 -translate-y-1/2 left-1/2 top-1/2
  }

  .hide-file-input {
    color: transparent !important;
    &::file-selector-button {
      display: none;
    }
  }
}

@layer base {
  input[type="date"] {
    &::-webkit-calendar-picker-indicator {
      background-color: #79a7b8;
      border-radius: 8px;
      padding: 5px;
      cursor: pointer;
      color-scheme: dark;
    }
  }

  .fig {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
}

@layer base {
  .custom-y-scrollbar-1 {
    --sb-track-color: #d4d4d4;
    --sb-thumb-color: #8a8a8a;
    --sb-size: 7px;
  }

  .custom-y-scrollbar-1::-webkit-scrollbar {
    width: var(--sb-size)
  }

  .custom-y-scrollbar-1::-webkit-scrollbar-track {
    background: var(--sb-track-color);
    border-radius: 10px;
  }

  .custom-y-scrollbar-1::-webkit-scrollbar-thumb {
    background: var(--sb-thumb-color);
    border-radius: 10px;

  }

  @supports not selector(::-webkit-scrollbar) {
    .custom-y-scrollbar-1 {
      scrollbar-color: var(--sb-thumb-color)
      var(--sb-track-color);
    }
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
