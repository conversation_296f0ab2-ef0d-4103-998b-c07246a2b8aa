<script lang="ts" setup>
import { Primitive, type PrimitiveProps } from 'radix-vue';
import { cn } from '@/shadcn-utils';
import type { HTMLAttributes } from 'vue';

const props = withDefaults(defineProps<PrimitiveProps & { class?: HTMLAttributes['class'] }>(), {
  as: 'a',
  class: '',
});
</script>

<template>
  <Primitive
    :as="as"
    :as-child="asChild"
    :class="cn('transition-colors hover:text-foreground', props.class)"
  >
    <slot />
  </Primitive>
</template>
