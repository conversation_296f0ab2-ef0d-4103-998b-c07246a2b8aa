<script setup lang="ts">
import { reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import PageLoader from '@/components/global/PageLoader.vue';
import { Button } from '@/shadcn-components/ui/button';
import Input from '@/shadcn-components/ui/inputs/Input.vue';
import { Label } from '@/shadcn-components/ui/label';
import adminApi from '@/util/adminAxios';
import { deployToast, ToastType } from '@/util/toast';
import type { Unit } from '@/util/types/api-responses';
import { ComponentStateType } from '@/util/types/components';

interface Props {
  dialogData?: Unit
}

const props = defineProps<Props>();

const emit = defineEmits(['onCreate']);

const isOpened = defineModel<boolean>();
const { t } = useI18n();
const componentState = ref(ComponentStateType.OK);

const unitForm = reactive({
  name: { ...props?.dialogData?.name,
  } ?? {
    sk: '',
    en: '',
    zh: '',
  },
  shortcut: { ...props?.dialogData?.shortcut,
  } ?? {
    sk: '',
    en: '',
    zh: '',
  },
});

const onSubmit = async() => {
  componentState.value = ComponentStateType.LOADING;
  try {
    if (props.dialogData) {
      await adminApi.put(`/api/admin/units/${props.dialogData.id}`, unitForm);
    } else {
      await adminApi.post('/api/admin/units', unitForm);
    }

    emit('onCreate');
    deployToast(ToastType.SUCCESS, {
      text: t('misc.success'),
      timeout: 6000,
    });
    isOpened.value = false;
  } catch {
    deployToast(ToastType.ERROR, {
      text: t('misc.error'),
      timeout: 6000,
    });
  }
  componentState.value = ComponentStateType.OK;
};
</script>

<template>
  <Teleport to="body">
    <el-dialog
      v-model="isOpened"
      width="fit-content"
      :show-close="false"
      header-class="hidden"
      class="!p-0 rounded-2xl"
    >
      <form autocomplete="off" class="relative bg-white p-6 rounded-2xl w-[40rem] max-w-[90vw] overflow-y-auto" @submit.prevent="onSubmit">
        <h3 class="font-bold text-xl mb-2">{{ dialogData ? $t('products.edit-unit') : $t('settings.new-measurement-unit') }}</h3>
        <div class="grid gap-4 py-4">
          <div class="flex items-center gap-4">
            <Label class="text-right w-24">
              {{ $t('misc.title') }}*
            </Label>

            <div class="flex items-center w-full">
              <Input
                v-model="unitForm.name"
                type="text"
                required
                multilang
              />
            </div>
          </div>

          <div class="flex items-center gap-4">
            <Label class="text-right w-24">
              {{ $t('misc.shortcut') }}*
            </Label>

            <div class="flex items-center w-full">
              <Input
                v-model="unitForm.shortcut"
                type="text"
                required
                multilang
              />
            </div>
          </div>

          <div class="flex items-center justify-end gap-2">
            <Button type="button" variant="default" class="bg-gray-400/80 hover:bg-gray-400" @click="isOpened = false">
              <span>{{ $t('misc.cancel') }}</span>
            </Button>
            <Button type="submit">
              {{ $t('misc.save') }}
            </Button>
          </div>
          <div v-if="componentState === ComponentStateType.LOADING" class="absolute left-0 top-0 w-full h-full bg-white/70 rounded-2xl">
            <PageLoader :absolute-center="true" />
          </div>
        </div>
      </form>
    </el-dialog>
  </Teleport>
</template>
