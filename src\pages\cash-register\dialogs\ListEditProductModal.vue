<script setup lang="ts">
import { IconMinus, IconPlus } from '@tabler/icons-vue';
import { onMounted, ref, watch } from 'vue';
import { useCashRegisterStore } from '@/stores/cash-register';
import cashApi from '@/util/cashAxios';
import { getFromMultiLangObject } from '@/util/multilang';
import type { CROrderItem } from '@/util/types/api-responses';

const store = useCashRegisterStore();

const emit = defineEmits(['close', 'openDiscountModal']);
const props = defineProps<{ product: CROrderItem }>();

const quantity = ref(props.product.quantity);

const remove = async() => {
  try {
    await cashApi.delete(`/api/cash-register/orders/${store.order?.id}/items/${props.product.id}`);
    store.loadCart();
  } catch (error) {
    // htmlLog(error);
  } finally {
    emit('close');
  }
};

const save = async() => {
  try {
    await cashApi.put(`/api/cash-register/orders/${store.order?.id}/items/${props.product.id}`, {
      quantity: quantity.value,
    });

    store.loadCart();
  } catch (error) {
    // htmlLog(error);
  } finally {
    emit('close');
  }
};

const fakePipnutie = () => {
  if (import.meta.env.DEV) {
    window.receiveResponse(`{"action":"dataFromReader","data":"bhqohek6bdu","inputDevice":{"name":"StrongLink USB CardReader","id":6,"vendorId":1241,"productId":5379,"descriptor":"ecb58b2358483198f0a3c079c9e3b4c60760f2a7","keyboardType":2,"sources":"-2147483391 (KEYBOARD)","controllerNumber":0,"isVirtual":false,"hasMicrophone":false}}`);
  }
};

watch(() => store.unchipTicketID, (unchipTicketID) => {
  if (!unchipTicketID) {
    emit('close');
  }
});

onMounted(() => {
  store.unchipTicketID = undefined;
});
</script>

<template>
  <div class="fixed inset-0 z-50 bg-black/65 grid place-items-center font-medium" @click.self="emit('close')">
    <div class="max-w-[385px]s w-fit bg-white p-6 rounded-xl">
      <div v-if="store.unchipTicketID" class="h-[230px] flex flex-col">
        <h2 class="text-2xl font-bold" @click="fakePipnutie">{{ getFromMultiLangObject(product?.name) }}</h2>

        <div class="flex-1 grid place-content-center">
          <span class="text-lg text-center">
            {{ $t('cash-register.unchip-message') }}
          </span>
        </div>
      </div>
      <div v-else class="space-y-6">
        <h2 class="text-2xl font-bold">{{ getFromMultiLangObject(product?.name) }}</h2>

        <div class="text-lg text-gray-500 !-mb-4">{{ $t('products.quantity') }}:</div>
        <div class="w-full flex">
          <div class="rounded-md flex">
            <button :disabled="quantity === 1 || quantity === product.tickets_count" class="disabled:opacity-40 disabled:active:bg-gray-50 relative border rounded-l-md text-center py-2 w-24 bg-gray-50 active:bg-gray-100 text-transparent border-r-0 focus:outline-none" @click="quantity--">
              <IconMinus stroke-width="2.5" class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-gray-500" />
            </button>
            <input v-model="quantity" type="number" class="border text-2xl max-w-40 text-center py-4 focus:outline-none">
            <button class="relative border rounded-r-md text-center w-24 bg-gray-50 active:bg-gray-100 text-transparent border-l-0 focus:outline-none" @click="quantity++">
              <IconPlus stroke-width="2.5" class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-gray-500" />
            </button>
          </div>
        </div>

        <div class="fig justify-end">
          <button v-if="product.tickets_count" class="bg-cyan-400 active:bg-cyan-600 text-white py-3.5 px-5 rounded-md text-lg" @click="store.unchipTicketID = product.id">{{ $t('cash-register.unchip') }}</button>
          <button class="bg-rose-500 active:bg-rose-600 text-white py-3.5 px-5 rounded-md text-lg" @click="remove">{{ $t('misc.delete') }}</button>
          <button class="bg-orange-400 active:bg-orange-500 text-white py-3.5 px-5 rounded-md text-lg" @click="emit('openDiscountModal', product)">{{ $t('cash-register.discount') }}</button>
          <button class="bg-emerald-500 active:bg-emerald-600 text-white py-3.5 px-5 rounded-md text-lg" @click="save">{{ $t('misc.save') }}</button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
</style>
