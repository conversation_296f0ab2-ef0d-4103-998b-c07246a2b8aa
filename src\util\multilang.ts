import { computed, reactive } from 'vue';
import i18n from '@/i18n';
import type { MultiLang } from '@/util/types/api-responses';

// Reactive state for i18n usage
export const useI18n = () => reactive({
  currentLang: computed(() => i18n.global.locale.value as string),
});

// Helper function to retrieve values from multi-language objects
export const getFromMultiLangObject = <T extends MultiLang | string | undefined>(obj: T) => {
  return computed(() => {
    if (!obj) {
      return '';
    }

    if (typeof obj === 'string') {
      return obj; // Directly return the string if `obj` is a string
    }

    const i18nLocale = useI18n();

    if (Object.prototype.hasOwnProperty.call(obj, i18nLocale.currentLang) && obj[i18nLocale.currentLang]) {
      return obj[i18nLocale.currentLang];
    }

    // Fallback to the first non-empty value in the object
    return Object.values(obj).find(elm => elm !== '') || '';
  });
};
