<script setup lang="ts">
import { IconLoader2, IconCircleCheck, IconExclamationCircle, IconRefresh } from '@tabler/icons-vue';
import { onBeforeUnmount, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import cashApi from '@/util/cashAxios';
import { CustomEvents } from '@/util/facades/custom-event';
import { deployToast, ToastType } from '@/util/toast';
import { AndroidActions } from '../androidRequestHandler';

const { t } = useI18n();
const emit = defineEmits(['close']);

const loading = ref(false);
const result = ref<'PENDING' | 'SUCCESS' | 'ERROR'>('PENDING');
const errorMessage = ref('');
const uniqueId = `id-${Date.now()}-${Math.random().toString(36).slice(2, 8)}`;

const createOnRegisterCommandHandler = (resolve: (value?: void | PromiseLike<void>) => void) => {
  return (e: Event) => {
    const { detail } = e as CustomEvent<{ action: string, id: string }>;

    if (detail.action !== AndroidActions.RESET_WITH_ZERO_RECEIPT || (detail.id !== uniqueId && (import.meta.env.DEV && detail.id !== 'simulator-test-id'))) {
      return;
    }

    resolve();
    return;
  };
};

const performReset = async() => {
  loading.value = true;
  result.value = 'PENDING';

  try {
    await cashApi.post('/api/cash-register/commands', {
      data: {
        action: AndroidActions.RESET_WITH_ZERO_RECEIPT,
        id: uniqueId,
      },
    });

    const { promise, resolve, reject } = Promise.withResolvers<void | undefined>();
    const handler = createOnRegisterCommandHandler(resolve);
    window.eventBus.addEventListener(CustomEvents.DataFromReaderEvent, handler);
    const timeoutId = setTimeout(() => {
      reject('Timeout');
    }, 15000);
    onBeforeUnmount(() => {
      window.eventBus.removeEventListener(CustomEvents.DataFromReaderEvent, handler);
      clearTimeout(timeoutId);
    });

    await promise;
    clearTimeout(timeoutId);
    window.eventBus.removeEventListener(CustomEvents.DataFromReaderEvent, handler);

    result.value = 'SUCCESS';
    deployToast(ToastType.SUCCESS, {
      text: t('cash-register.zero-receipt-reset-successful'),
      timeout: 6000,
    });

    setTimeout(() => {
      emit('close');
    }, 5000);
  } catch (error: any) {
    result.value = 'ERROR';
    errorMessage.value = error.response?.data?.message || t('cash-register.zero-receipt-reset-failed');
    deployToast(ToastType.ERROR, {
      text: errorMessage.value,
      timeout: 6000,
    });
  } finally {
    loading.value = false;
  }
};

const tryAgain = () => {
  result.value = 'PENDING';
  errorMessage.value = '';
};
</script>

<template>
  <div class="fixed inset-0 z-50 bg-black/65 grid place-items-center" @click.self="emit('close')">
    <div class="max-w-[500px] w-full bg-white p-10 rounded-xl">
      <div class="space-y-6">
        <h2 class="text-xl font-bold text-center">{{ $t('cash-register.zero-receipt-reset') }}</h2>

        <!-- Success State -->
        <div v-if="result === 'SUCCESS'" class="text-center space-y-4">
          <IconCircleCheck class="mx-auto h-16 w-16 text-green-500" />
          <h3 class="text-lg font-bold text-green-600">{{ $t('cash-register.zero-receipt-reset-successful') }}</h3>
          <p class="text-gray-600">{{ $t('cash-register.modal-will-close-automatically') }}</p>
          <button class="w-full bg-green-500 py-3 font-medium text-white rounded-xl hover:bg-green-600 transition-colors" @click="emit('close')">
            {{ $t('misc.close') }}
          </button>
        </div>

        <!-- Error State -->
        <div v-else-if="result === 'ERROR'" class="text-center space-y-4">
          <IconExclamationCircle class="mx-auto h-16 w-16 text-red-500" />
          <h3 class="text-lg font-bold text-red-600">{{ $t('cash-register.zero-receipt-reset-failed') }}</h3>
          <p class="text-gray-600">{{ errorMessage }}</p>
          <div class="flex gap-4">
            <button class="flex-1 bg-gray-500 py-3 font-medium text-white rounded-xl hover:bg-gray-600 transition-colors" @click="emit('close')">
              {{ $t('misc.close') }}
            </button>
            <button class="flex-1 bg-blue-500 py-3 font-medium text-white rounded-xl hover:bg-blue-600 transition-colors" @click="tryAgain">
              {{ $t('misc.try-again') }}
            </button>
          </div>
        </div>

        <!-- Pending State -->
        <div v-else class="text-center space-y-6">
          <div v-if="loading" class="space-y-4">
            <IconLoader2 class="animate-spin h-12 w-12 mx-auto text-blue-500" />
            <p class="text-gray-600">{{ $t('cash-register.processing-reset') }}</p>
          </div>

          <div v-else class="space-y-4">
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div class="flex items-center gap-2 text-yellow-800">
                <IconExclamationCircle class="h-5 w-5" />
                <span class="font-medium">{{ $t('misc.warning') }}</span>
              </div>
              <p class="text-yellow-700 mt-2">{{ $t('cash-register.zero-receipt-reset-warning') }}</p>
            </div>

            <div class="text-gray-600">
              <p>{{ $t('cash-register.zero-receipt-reset-description') }}</p>
            </div>

            <div class="flex gap-4">
              <button class="flex-1 bg-gray-500 py-3 font-medium text-white rounded-xl hover:bg-gray-600 transition-colors" @click="emit('close')">
                {{ $t('misc.cancel') }}
              </button>
              <button
                class="flex-1 bg-orange-500 py-3 font-medium text-white rounded-xl hover:bg-orange-600 transition-colors flex items-center justify-center gap-2"
                @click="performReset"
              >
                <IconRefresh class="h-5 w-5" />
                {{ $t('cash-register.perform-reset') }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
