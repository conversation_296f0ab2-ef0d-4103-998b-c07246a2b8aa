<script setup lang="ts">
import { Download } from 'lucide-vue-next';
import { ref } from 'vue';
import Button from '@/shadcn-components/ui/button/Button.vue';
import adminApi from '@/util/adminAxios';

const emit = defineEmits(['getImg']);
const props = defineProps<{
  coverImageUrl: string | null;
  disabled?: boolean;
}>();

const imageUrl = ref(props.coverImageUrl);

const sendImage = async(files: FileList) => {
  const formData = new FormData();
  formData.append('file', files[0]);
  formData.append('public_url', '1');

  const { data } = await adminApi.post('/api/admin/media/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  imageUrl.value = data.files[0].public_url;
  emit('getImg', data.files[0].path);
};

const onDrop = async(e: DragEvent) => {
  e.preventDefault();
  e.stopPropagation();
  const files = e.dataTransfer?.files;
  if (!files) {
    return;
  }

  await sendImage(files);
};

const onInput = async(e: Event) => {
  const files = (e.target as HTMLInputElement).files;
  if (!files) {
    return;
  }

  await sendImage(files);
};

const removeImage = () => {
  imageUrl.value = '';
  emit('getImg', null);
};
</script>

<template>
  <div class="relative flex-grow h-[330px]">
    <div class="absolute inset-0 bg-white border-[3px] border-dashed rounded-md">
      <div v-if="imageUrl" class="absolute inset-0">
        <img v-if="imageUrl" :src="imageUrl" alt="image preview" class="h-full w-full rounded-md object-scale-down">
      </div>

      <div v-else class="flex flex-col items-center justify-center h-full">
        <Download :size="54" stroke-width="2.5" class="text-slate-300" />
        <p class="font-medium mt-2 text-slate-300">{{ $t('misc.drag-in-photo') }}</p>
      </div>
    </div>
    <input id="file" class="hide-file-input w-full h-full" :disabled :class="imageUrl || 'opacity-0'" type="file" @input="onInput" @drop="onDrop">

    <div v-if="imageUrl" class="flex gap-2 mt-2 w-full justify-end font-medium">
      <label for="file" class="bg-paynes-gray text-primary-foreground hover:bg-paynes-gray/90 h-10s px-4 rounded-md py-2 pt-2.5 text-sm">{{ $t('misc.upload-new') }}</label>
      <Button variant="destructive" @click.prevent="removeImage">{{ $t('misc.remove') }}</Button>
    </div>
  </div>
</template>
