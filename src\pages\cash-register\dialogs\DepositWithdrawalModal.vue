<script setup lang="ts">
import { IconArrowBarDown, IconArrowBarUp, IconArrowLeft, IconCash, IconGiftCard, IconLoader2, IconRefresh } from '@tabler/icons-vue';
import { computed, onUnmounted, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import Keypad from '@/pages/cash-register/dialogs/components/Keypad.vue';
import { useCashRegisterStore } from '@/stores/cash-register';
import cashApi from '@/util/cashAxios';

const { t } = useI18n();
const store = useCashRegisterStore();

const emit = defineEmits(['close']);

const loader = ref(false);
const timeoutId = ref<ReturnType<typeof setTimeout>>();
const creatingNewType = ref<'DEPOSIT' | 'WITHDRAWAL'>();
const method = ref<'CASH' | 'VOUCHER'>('CASH');
const amount = ref<string[]>([]);

const confirm = async() => {
  loader.value = true;
  timeoutId.value = setTimeout(() => {
    store.commandResponse = {
      state: 'ERROR',
      messages: [
        {
          message: t('cash-register.transaction-timeout'),
          code: 'TIMEOUT',
        },
      ],
    };
  }, 30 * 1000);

  try {
    await cashApi.post('api/cash-register/transactions/request', {
      type: creatingNewType.value,
      amount: Number(amountFormatted.value),
      currency: 'EUR',
      method: method.value,
    });
  } catch {
    store.commandResponse = {
      state: 'ERROR',
      messages: [],
    };

    if (timeoutId.value) {
      clearTimeout(timeoutId.value);
    }
  }
};

const close = () => {
  if (!loader.value) {
    emit('close');
  }
};

const tryAgain = () => {
  store.commandResponse = undefined;
};

watch(() => store.commandResponse, res => {
  loader.value = false;
  if (timeoutId.value) {
    clearTimeout(timeoutId.value);
  }
  if (res && res.state === 'OK') {
    emit('close');
  }
}, { deep: true });

onUnmounted(() => {
  if (timeoutId.value) {
    clearTimeout(timeoutId.value);
  }

  store.commandResponse = undefined;
});

const amountFormatted = computed(() => {
  const tmp = Number(amount.value.join(''));
  return isNaN(tmp) ? '0.' : amount.value.at(-1) === '.' ? amount.value.join('') : tmp;
});
</script>

<template>
  <div class="fixed inset-0 z-50 bg-black/65 grid place-items-center" @click.self="emit('close')">
    <div class="max-w-[520px] w-full bg-white p-10 rounded-xl">

      <div v-if="creatingNewType && !store.commandResponse" class="space-y-4">
        <h2 class="text-xl font-bold">{{ $t(`cash-register.${creatingNewType.toLowerCase()}`) }} - {{ $t(`cash-register.${method.toLowerCase()}`) }}</h2>
        <button class="flex items-center text-gray-500 py-3 !mt-0 !-mb-3" @click="creatingNewType = undefined; amount = []">
          <IconArrowLeft />
          <span class="ml-2">{{ $t('cash-register.back') }}</span>
        </button>

        <div class="fig gap-4 font-medium">
          <button :class="[method === 'CASH' ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-600', 'w-full rounded-md py-3.5 flex items-center justify-center gap-2']" @click="method = 'CASH'">
            <IconCash />
            <span>{{ $t('cash-register.cash') }}</span>
          </button>
          <button :class="[method === 'VOUCHER' ? 'bg-amber-400 text-white' : 'bg-gray-200 text-gray-600', 'w-full rounded-md py-3.5 flex items-center justify-center gap-2']" @click="method = 'VOUCHER'">
            <IconGiftCard />
            <span>{{ $t('cash-register.voucher') }}</span>
          </button>
        </div>

        <div class="bg-slate-200 p-5 rounded-xl space-y-3">
          <div class="text-center text-sm text-gray-500">{{ $t('misc.amount') }}</div>
          <div class="text-3xl font-bold text-center">{{ amountFormatted }} €</div>
        </div>

        <Keypad v-model="amount" with-dot />

        <button :disabled="!Number(amountFormatted)" class="w-full disabled:grayscale bg-emerald-500 py-4 font-medium text-white rounded-xl text-xl fig justify-center" @click="confirm">
          <IconLoader2 v-if="loader" class="animate-spin" />
          <span>
            {{ $t('misc.confirm') }}
            <span class="lowercase">{{ $t(`cash-register.${creatingNewType.toLowerCase()}`) }}</span>
          </span>
        </button>
      </div>
      <div v-else-if="!creatingNewType && !store.commandResponse" class="space-y-4">
        <h2 class="text-xl font-bold">{{ $t('cash-register.transaction-history') }}</h2>

        <div class="fig gap-4 text-xl font-medium">
          <button class="bg-green-600 text-white w-full rounded-md py-5 px-6 flex items-center justify-center gap-2" @click="creatingNewType = 'DEPOSIT'">
            <IconArrowBarDown />
            <span>{{ $t('cash-register.new-deposit') }}</span>
          </button>
          <button class="bg-orange-400 text-white w-full rounded-md py-5 px-6 flex items-center justify-center gap-2" @click="creatingNewType = 'WITHDRAWAL'">
            <IconArrowBarUp />
            <span>{{ $t('cash-register.new-withdrawal') }}</span>
          </button>
        </div>
      </div>
      <div v-if="store.commandResponse && store.commandResponse?.state !== 'OK'">
        <div class="bg-slate-200 p-5 rounded-xl space-y-3">
          <div class="font-bold text-xl text-center text-red-500">{{ $t('cash-register.report-failed') }}</div>
          <div v-for="({ message, code }, index) in store.commandResponse?.messages" :key="index" class="flex gap-2 items-center">
            <span class="font-medium">{{ message }}</span>
            <span class="text-sm text-gray-500 mt-0.5">({{ code }})</span>
          </div>
        </div>

        <div class="mt-6 fig text-lg">
          <button class="w-full border py-3 font-medium rounded-xl" @click="close">
            <span>{{ $t('misc.close') }}</span>
          </button>
          <button class="w-full bg-blue-500 py-3 font-medium text-white rounded-xl fig justify-center" @click="tryAgain">
            <IconRefresh />
            <span>{{ $t('misc.try-again') }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
