<script setup lang="ts">
import { Edit, Trash2 } from 'lucide-vue-next';
import { onMounted, reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import PageLoader from '@/components/global/PageLoader.vue';
import UnitDialog from '@/pages/general-settings/dialogs/UnitDialog.vue';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/shadcn-components/ui/alert-dialog';
import { But<PERSON>, Button as ShadCnButton } from '@/shadcn-components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/shadcn-components/ui/table';
import adminApi from '@/util/adminAxios';
import { getFromMultiLangObject } from '@/util/multilang';
import { deployToast, ToastType } from '@/util/toast';
import type { Unit } from '@/util/types/api-responses';

const { t, locale } = useI18n();

const units = ref<Unit[]>([]);
const loading = ref(false);

const dialogData = reactive<{ modalShown: boolean; data: undefined | Unit }>({
  modalShown: false,
  data: undefined,
});

const getUnits = async() => {
  const to = setTimeout(() => {
    loading.value = true;
  }, 220);
  const { data } = await adminApi.get('/api/admin/units');
  clearTimeout(to);
  loading.value = false;
  return data.data;
};

const newUnit = () => {
  dialogData.modalShown = true;
  dialogData.data = undefined;
};

const editRow = (unit: Unit) => {
  dialogData.modalShown = true;
  dialogData.data = unit;
};

const removeUnit = async(id: string) => {
  try {
    await adminApi.delete(`/api/admin/units/${id}`);
    units.value = await getUnits();
  } catch {
    deployToast(ToastType.ERROR, {
      text: t('misc.error'),
      timeout: 6000,
    });
  }
};

onMounted(async() => {
  units.value = await getUnits();
});
</script>

<template>
  <div v-if="loading" class="w-full md:min-w-[500px]">
    <PageLoader absolute-center />
  </div>
  <div v-else class="p-4 relative w-full md:min-w-[500px]">
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>ID</TableHead>
          <TableHead>{{ $t('misc.title') }}</TableHead>
          <TableHead>{{ $t('misc.shortcut') }}</TableHead>
          <TableHead class="text-right">
            {{ $t('user-management.actions') }}
          </TableHead>
        </TableRow>
      </TableHeader>

      <TableBody>
        <TableRow v-for="unit in units" :key="unit.id">
          <TableCell v-if="unit.id" class="font-semibold">
            {{ unit.id }}
          </TableCell>
          <TableCell v-else>-</TableCell>

          <TableCell v-if="unit.name">
            {{ getFromMultiLangObject(unit.name).value }}
          </TableCell>
          <TableCell v-else>-</TableCell>

          <TableCell v-if="unit.shortcut">
            {{ getFromMultiLangObject(unit.shortcut).value }}
          </TableCell>
          <TableCell v-else>-</TableCell>

          <TableCell class="text-right w-[110px]">
            <div class="inline-flex items-center mr-2">
              <ShadCnButton class="size-8 p-1.5 rounded-full" variant="default" @click.stop.prevent="editRow(unit)">
                <Edit class="w-full h-full" />
              </ShadCnButton>
            </div>

            <AlertDialog>
              <AlertDialogTrigger @click.prevent.stop>
                <ShadCnButton class="w-8 h-8 p-1.5 rounded-full" variant="destructive">
                  <Trash2 class="w-full h-full" />
                </ShadCnButton>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>{{ $t('misc.delete-action', { name: unit.name[locale] }) }}</AlertDialogTitle>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>{{ $t('misc.cancel') }}</AlertDialogCancel>
                  <AlertDialogAction class="bg-destructive text-destructive-foreground hover:bg-destructive/90" @click="removeUnit(unit.id)">{{ $t('misc.continue') }}</AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </TableCell>
        </TableRow>
      </TableBody>
    </Table>

    <Button class="w-full mt-4" @click="newUnit">
      {{ $t('misc.add') }}
    </Button>
  </div>

  <suspense v-if="dialogData.modalShown">
    <UnitDialog v-model="dialogData.modalShown" :dialog-data="dialogData.data" @on-create="async () => units = await getUnits()" />
  </suspense>
</template>
