{"name": "antik-smartcity-portal-turnstile-app-ui", "private": true, "version": "0.0.1-beta", "type": "module", "scripts": {"dev": "vite --port 8282", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix && pnpm type-check", "type-check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "dependencies": {"@antik-web/axios-auth-refresh-response-data": "^4.0.0", "@babel/runtime": "^7.25.6", "@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-regular-svg-icons": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/vue-fontawesome": "^3.0.8", "@headlessui/vue": "^1.7.23", "@intlify/unplugin-vue-i18n": "^4.0.0", "@mdi/font": "^7.4.47", "@tabler/icons-vue": "^3.34.0", "@unhead/vue": "^1.10.0", "@vueuse/components": "^13.5.0", "@vueuse/core": "^11.0.3", "@vueuse/router": "^12.5.0", "axios": "^1.7.5", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "dayjs": "^1.11.13", "element-plus": "^2.9.7", "gridstack": "^12.2.2", "lodash-es": "^4.17.21", "lucide-vue-next": "^0.436.0", "luxon": "^3.5.0", "pinia": "^2.2.2", "radix-vue": "^1.9.5", "reka-ui": "^2.2.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vite-plugin-vuetify": "^2.0.4", "vue": "^3.4.38", "vue-api-query": "^1.11.0", "vue-dndrop": "^1.3.1", "vue-easy-lightbox": "^1.19.0", "vue-i18n": "^9.14.0", "vue-router": "^4.4.3", "vue-toastification": "2.0.0-rc.5", "vue3-treeselect": "^0.1.10"}, "devDependencies": {"@antik-vite/laman-sync": "^1.0.0", "@faker-js/faker": "^8.4.1", "@laravel/echo-vue": "^2.1.6", "@rushstack/eslint-patch": "^1.10.4", "@tailwindcss/container-queries": "^0.1.1", "@tsconfig/node20": "^20.1.4", "@types/lodash-es": "^4.17.12", "@types/luxon": "^3.4.2", "@types/node": "^22.5.1", "@typescript-eslint/eslint-plugin": "^8.3.0", "@typescript-eslint/parser": "^8.3.0", "@vitejs/plugin-vue": "^5.1.3", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-import": "^2.29.1", "eslint-plugin-import-newlines": "^1.4.0", "eslint-plugin-vue": "^9.27.0", "sass": "^1.77.8", "tailwindcss": "^3.4.10", "typescript": "^5.5.4", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "vite-svg-loader": "^5.1.0", "vue-tsc": "^2.1.2"}, "packageManager": "pnpm@9.7.0"}