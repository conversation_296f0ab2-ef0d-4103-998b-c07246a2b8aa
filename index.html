<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1.0, minimum-scale=1.0, viewport-fit=cover">
    <title>Cash register</title>
    <link rel="icon" type="image/png" href="/favicon-48x48.png" sizes="48x48" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="manifest" href="/favicon/site.webmanifest" />
    <meta name="msapplication-TileColor" content="#da532c">
    <meta name="msapplication-config" content="/favicon/browserconfig.xml">
    <meta name="theme-color" content="#ffffff">
  </head>
  <body>
    <pre id="log"></pre>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>

    <script type="text/javascript">
      function receiveResponse(jsonString) {
        let payload
        try {
            payload = JSON.parse(jsonString)
        } catch {
            payload = jsonString
        }

        // const log = document.createElement('div');
        // log.innerText = 'zavolala sa funkcia od roba receiveResponse';
        // document.getElementById('log')?.appendChild(log);

        window.dispatchEvent(new CustomEvent('android-data', { detail: payload }))
      }
    </script>
  </body>
</html>
