<script setup lang="ts">
import { useVModel } from '@vueuse/core';
import type { HTMLAttributes } from 'vue';

const props = defineProps<{
  defaultValue?: any
  modelValue?: any
  disabled?: boolean
  placeholder?: string
  errors?: string[]
  min?: string
  class?: HTMLAttributes['class']
}>();

const emits = defineEmits<{(e: 'update:modelValue', payload: string | number): void
}>();

const modelValue = useVModel(props, 'modelValue', emits, {
  passive: true,
  defaultValue: props.defaultValue,
});
</script>

<template>
  <div class="w-full">
    <el-date-picker v-model="modelValue" lang="sk" :min :disabled :placeholder class="!w-full" />
    <div v-for="error in errors" :key="error" class="text-rose-500 text-xs mt-0.5 ml-1.5">{{ error }}</div>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-input__wrapper) {
  min-height: 40px !important;
  padding: 8px 14px !important;
  border-radius: 0.375rem !important;
  border: 1px solid hsl(var(--input)) !important;
  box-shadow:  rgba(0, 0, 0, 0) 0 0 0 0, rgba(0, 0, 0, 0) 0 0 0 0, rgba(0, 0, 0, 0.1) 0 1px 3px 0, rgba(0, 0, 0, 0.1) 0 1px 2px -1px !important;
  transition: none !important;
  &:focus-within {
    outline: 2px solid #d2e2e7 !important;
    outline-offset: -3px;
    border: 1px solid transparent !important;
    border-radius: 0.475rem !important;
  }
}
</style>
