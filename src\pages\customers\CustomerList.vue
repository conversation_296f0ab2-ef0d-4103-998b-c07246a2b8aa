<script setup lang="ts">
import { IconCircleCheckFilled, IconCircleXFilled } from '@tabler/icons-vue';
import { useRouteQuery } from '@vueuse/router';
import debounce from 'lodash-es/debounce';
import { PlusIcon, Trash2, Edit, Search } from 'lucide-vue-next';
import { ref } from 'vue';
import PageLoader from '@/components/global/PageLoader.vue';
import Pagination from '@/components/global/Pagination.vue';
import { routeMap } from '@/router/routes';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/shadcn-components/ui/alert-dialog';
import { Button as ShadCnButton } from '@/shadcn-components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/shadcn-components/ui/table';
import { useAuthStore } from '@/stores/auth-store';
import adminApi from '@/util/adminAxios';
import { getFromMultiLangObject } from '@/util/multilang';
import type { Customer, Meta, Response } from '@/util/types/api-responses';
import { ComponentStateType } from '@/util/types/components';

const authStore = useAuthStore();
const currentPage = useRouteQuery('page');
const currentLimit = useRouteQuery('limit');

const componentState = ref(ComponentStateType.LOADING);
const customers = ref<Customer[]>([]);
const paginationMeta = ref<Meta>();

const fetchData = async(args?: { page?: number, limit?: number, search?: string }) => {
  const to = setTimeout(() => {
    componentState.value = ComponentStateType.LOADING;
  }, 220);
  try {
    const params = {
      page: (args?.page ?? currentPage.value) ?? 1,
      limit: (args?.limit ?? currentLimit.value) ?? 15,
    } as Record<string, string | number>;
    if (args?.search && args.search.length > 2) {
      params['filter[search]'] = args.search;
      delete params.page;
    }

    const { data } = await adminApi.get<Response<Customer[]>>('/api/admin/customers', { params });

    customers.value = data.data;
    paginationMeta.value = data.meta;
    clearTimeout(to);
    componentState.value = ComponentStateType.OK;
  } catch {
    clearTimeout(to);
    componentState.value = ComponentStateType.ERROR;
  }
};

await fetchData();

const removeCustomer = async(id: string) => {
  try {
    await adminApi.delete(`/api/admin/customers/${id}`);
    await fetchData();
  } catch {
    componentState.value = ComponentStateType.ERROR;
  }
};

const onSearchInput = debounce((ev: Record<any, any>) => {
  fetchData({ search: ev.target?.value });
}, 500);
</script>

<template>
  <div class="flex flex-col">
    <div v-if="componentState === ComponentStateType.OK" class="flex flex-col">
      <div class="flex items-center gap-4 mb-2 flex-wrap">
        <router-link :class="{ 'pointer-events-none select-none opacity-50': !authStore.hasPermission('customer manage') }" :to="{ name: routeMap.customers.children.create.name }" class="w-fit bg-gray-200 rounded-lg p-2 hover:bg-gray-300 transition-colors gap-0.5 flex items-center cursor-pointer">
          <PlusIcon class="aspect-square text-gray-500" />
          <div class="text-sm font-medium text-gray-600">{{ $t('customers.new-customer') }}</div>
        </router-link>
        <div class="relative p-2 bg-white border-gray-300 border rounded-lg flex items-center w-64 overflow-hidden flex-1 sm:flex-none">
          <div class="pl-0.5 pr-1.5">
            <Search class="min-h-5 h-5 min-w-5 w-5 text-muted-foreground bg-mute" />
          </div>
          <input
            type="text"
            :placeholder="$t('misc.search')"
            class="w-full focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-gray-500 outline-none placeholder:text-sm"
            @input="onSearchInput"
          >
        </div>
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead class="text-center">#</TableHead>
            <TableHead>{{ $t('misc.name') }}</TableHead>
            <TableHead>{{ $t('misc.mail') }}</TableHead>
            <TableHead>{{ $t('misc.address') }}</TableHead>
            <TableHead>{{ $t('misc.city') }}</TableHead>
            <TableHead>{{ $t('customers.customer_groups') }}</TableHead>
            <TableHead>{{ $t('customers.can_invoice_order') }}</TableHead>
            <TableHead>{{ $t('products.active') }}</TableHead>
            <TableHead class="text-right">
              {{ $t('user-management.actions') }}
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-for="(customer, idx) in customers" :key="idx">
            <TableCell class="!py-0 w-6 text-center text-gray-500">
              <div>{{ idx + 1 }}.</div>
            </TableCell>

            <TableCell v-if="customer.name" class="font-semibold !py-0">
              <router-link :to="{ name: routeMap.customers.children.edit.name, params: { id: customer.id } }">
                {{ customer.name }}
              </router-link>
            </TableCell>
            <TableCell v-else>-</TableCell>

            <TableCell v-if="customer.email">
              <router-link :to="{ name: routeMap.customers.children.edit.name, params: { id: customer.id } }">
                {{ customer.email }}
              </router-link>
            </TableCell>
            <TableCell v-else>-</TableCell>

            <TableCell v-if="customer.address">
              <router-link :to="{ name: routeMap.customers.children.edit.name, params: { id: customer.id } }">
                {{ customer.address }}
              </router-link>
            </TableCell>
            <TableCell v-else>-</TableCell>

            <TableCell v-if="customer.city">
              <router-link :to="{ name: routeMap.customers.children.edit.name, params: { id: customer.id } }">
                {{ customer.city }}
              </router-link>
            </TableCell>
            <TableCell v-else>-</TableCell>

            <TableCell v-if="customer.customer_groups?.length" class="flex-wrap space-x-2 space-y-2 space-x-reverse [&>div:first-child]:mr-2 max-w-[600px]">
              <div v-for="{ id, name } in customer.customer_groups" :key="id" class="inline-flex py-1 px-2 bg-orange-400 text-white rounded">{{ name }}</div>
            </TableCell>
            <TableCell v-else>-</TableCell>

            <TableCell class="w-6">
              <IconCircleCheckFilled v-if="customer.can_invoice_order" class="text-green-500 mx-auto" />
              <IconCircleXFilled v-else class="text-red-500 mx-auto" />
            </TableCell>

            <TableCell class="w-6">
              <IconCircleCheckFilled v-if="customer.active" class="text-green-500 mx-auto" />
              <IconCircleXFilled v-else class="text-red-500 mx-auto" />
            </TableCell>

            <TableCell class="text-rights w-[110px] ml-auto flex gap-2 justify-end">
              <div class="inline-flex items-center">
                <router-link :to="{ name: routeMap.customers.children.edit.name, params: { id: customer.id } }" class="size-8 p-1.5 rounded-full bg-paynes-gray text-primary-foreground hover:bg-paynes-gray/90">
                  <Edit class="w-full h-full" />
                </router-link>
              </div>

              <AlertDialog>
                <AlertDialogTrigger :disabled="!authStore.hasPermission('product manage')" @click.prevent.stop>
                  <ShadCnButton class="w-8 h-8 p-1.5 rounded-full" variant="destructive">
                    <Trash2 class="w-full h-full" />
                  </ShadCnButton>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>{{ $t('form.delete-customer', { name: getFromMultiLangObject(customer.name).value }) }}</AlertDialogTitle>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>{{ $t('misc.cancel') }}</AlertDialogCancel>
                    <AlertDialogAction class="bg-destructive text-destructive-foreground hover:bg-destructive/90" @click="removeCustomer(customer.id)">{{ $t('misc.continue') }}</AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>

      <Pagination v-if="customers?.length" :meta="paginationMeta!" @new-page="fetchData" />

      <div v-if="customers?.length === 0" class="text-sm text-gray-400 w-full text-center mt-4">
        {{ $t('misc.list-is-empty') }}
      </div>
    </div>
    <div v-if="componentState === ComponentStateType.LOADING" class="absolute bg-white/70 top-0 left-0 w-full h-full rounded-3xl">
      <PageLoader :fixed-center="true" />
    </div>
    <div v-else-if="componentState === ComponentStateType.ERROR" class="absolute-center bg-black text-white font-bold p-1">
      <span>{{ $t('misc.failed-to-get-data') }}!</span>
    </div>
  </div>
</template>
