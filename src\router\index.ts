import { createRouter, createWebHistory } from 'vue-router';
import { routeMap, routes } from '@/router/routes';
import { useAuthStore } from '@/stores/auth-store';
import type { RouteRecordNormalized } from 'vue-router';

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else if (to.name === from.name) {
      return;
    } else {
      return { top: 0 };
    }
  },
  routes,
});

router.beforeEach(async(to, from, next) => {
  const authStore = useAuthStore();

  const isCashRoute = to.matched.some(route =>
    typeof route.name === 'string' && route.name.includes('cash-register'),
  );

  const isAuthNotRequired = to.meta?.authNotRequired;

  if (isCashRoute) {
    return next();
  } else {
    if (authStore.user || isAuthNotRequired || to.name === routeMap.home.name) {
      return next();
    } else {
      return next({
        name: routeMap.home.name,
        query: { ...to.query, redirect: to.fullPath },
      });
    }
  }
});

export default router;

export type RouteMetaNormalized = {
  meta: {
    //
  }
} & RouteRecordNormalized;
