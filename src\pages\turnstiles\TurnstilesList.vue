<script setup lang="ts">
import { IconCircleCheckFilled, IconCircleXFilled } from '@tabler/icons-vue';
import { useRouteQuery } from '@vueuse/router';
import debounce from 'lodash-es/debounce';
import { Search } from 'lucide-vue-next';
import { ref, reactive, onMounted } from 'vue';
import PageLoader from '@/components/global/PageLoader.vue';
import Pagination from '@/components/global/Pagination.vue';
import { Button as ShadCnButton } from '@/shadcn-components/ui/button';
import Input from '@/shadcn-components/ui/inputs/Input.vue';
import Select from '@/shadcn-components/ui/inputs/Select.vue';
import { Label } from '@/shadcn-components/ui/label';
import { Switch } from '@/shadcn-components/ui/switch';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/shadcn-components/ui/table';
import adminApi from '@/util/adminAxios';
import type { Meta, Response, Turnstile, TurnstileCategory } from '@/util/types/api-responses';
import { ComponentStateType } from '@/util/types/components';

const currentPage = useRouteQuery('page');
const currentLimit = useRouteQuery('limit');

const componentState = ref(ComponentStateType.LOADING);
const turnstiles = ref<Turnstile[]>([]);
const paginationMeta = ref<Meta>();
const editingId = ref<string | null>(null);
const editingData = ref<Partial<Turnstile>>({});
const categories = ref<TurnstileCategory[]>([]);

const getCategories = async() => {
  try {
    const { data } = await adminApi.get('/api/admin/turnstiles/categories?limit=100');
    return data.data;
  } catch {
    return [];
  }
};

const scannerModal = reactive({
  isOpen: false,
  turnstileId: '',
  id: '',
  used: false,
  default_categories: [] as string[],
  default_category_id_out: '',
  settings: {
    reverse: false,
    readers_flip: false,
    readers_in: {
      rfid: true,
      mifare: true,
      qr: true,
    },
    readers_out: {
      rfid: true,
      mifare: true,
      qr: true,
    },
  },
});

const fetchData = async(args?: { page?: number, limit?: number, search?: string }) => {
  const to = setTimeout(() => {
    componentState.value = ComponentStateType.LOADING;
  }, 220);
  try {
    const params = {
      page: (args?.page ?? currentPage.value) ?? 1,
      limit: (args?.limit ?? currentLimit.value) ?? 15,
    } as Record<string, string | number>;
    if (args?.search && args.search.length > 2) {
      params.search = args.search;
      delete params.page;
    }

    const { data } = await adminApi.get<Response<Turnstile[]>>('/api/admin/turnstiles', { params });

    turnstiles.value = data.data;
    paginationMeta.value = data.meta;
    clearTimeout(to);
    componentState.value = ComponentStateType.OK;
  } catch {
    clearTimeout(to);
    componentState.value = ComponentStateType.ERROR;
  }
};

await fetchData();

const startEdit = (turnstile: Turnstile) => {
  editingId.value = turnstile.sn;
  editingData.value = { ...turnstile };
};

const cancelEdit = () => {
  editingId.value = null;
  editingData.value = {};
};

const saveEdit = async() => {
  if (!editingId.value) {
    return;
  }

  try {
    await adminApi.put(`/api/admin/turnstiles/${editingId.value}`, editingData.value);
    await fetchData();
    editingId.value = null;
    editingData.value = {};
  } catch {
    // Handle error
  }
};

const openScannerSettings = (turnstile: Turnstile) => {
  scannerModal.turnstileId = turnstile.sn;
  scannerModal.id = turnstile.id;
  scannerModal.used = turnstile.used;
  scannerModal.default_categories = turnstile.default_categories?.map(cat => cat?.id);
  scannerModal.default_category_id_out = turnstile.default_category_id_out?.id;
  scannerModal.settings = turnstile.settings ?? {
    readers_flip: false,
    reverse: false,
    readers_in: { rfid: true, mifare: true, qr: true },
    readers_out: { rfid: true, mifare: true, qr: true },
  };
  scannerModal.isOpen = true;
};

const saveScannerSettings = async() => {
  try {
    await adminApi.put(`/api/admin/turnstiles/${scannerModal.turnstileId}`, scannerModal);
    await fetchData();
    scannerModal.isOpen = false;
  } catch {
    // Handle error
  }
};

const onSearchInput = debounce((ev: Record<any, any>) => {
  fetchData({ search: ev.target?.value });
}, 500);

onMounted(async() => {
  categories.value = await getCategories();
});

</script>

<template>
  <div class="flex flex-col">
    <div v-if="componentState === ComponentStateType.OK" class="flex flex-col">
      <div class="flex items-center gap-4 mb-2 flex-wrap">
        <div class="relative p-2 bg-white border-gray-300 border rounded-lg flex items-center w-64 overflow-hidden flex-1 sm:flex-none">
          <div class="pl-0.5 pr-1.5">
            <Search class="min-h-5 h-5 min-w-5 w-5 text-muted-foreground bg-mute" />
          </div>
          <input
            type="text"
            :placeholder="$t('misc.search')"
            class="w-full focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-gray-500 outline-none placeholder:text-sm"
            @input="onSearchInput"
          >
        </div>
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{{ $t('turnstiles.serial-number') }}</TableHead>
            <TableHead>{{ $t('turnstiles.id') }}</TableHead>
            <TableHead>{{ $t('turnstiles.ip-address') }}</TableHead>
            <TableHead>{{ $t('turnstiles.mac-address') }}</TableHead>
            <TableHead>{{ $t('turnstiles.connection') }}</TableHead>
            <TableHead>{{ $t('turnstiles.status') }}</TableHead>
            <TableHead>{{ $t('turnstiles.config') }}</TableHead>
            <TableHead>{{ $t('turnstiles.reverse') }}</TableHead>
            <TableHead>{{ $t('turnstiles.used') }}</TableHead>
            <TableHead class="text-right">{{ $t('user-management.actions') }}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-for="turnstile in turnstiles" :key="turnstile.sn">
            <TableCell>
              {{ turnstile.sn }}
            </TableCell>

            <TableCell>
              <Input
                v-if="editingId === turnstile.sn"
                v-model="editingData.id"
                class="w-16"
              />
              <span v-else>{{ turnstile.id }}</span>
            </TableCell>

            <TableCell>{{ turnstile.ip_address }}</TableCell>
            <TableCell>{{ turnstile.mac_address }}</TableCell>
            <TableCell>
              <span :class="turnstile.connect === 'ONLINE' ? 'text-green-600' : 'text-red-600'">
                {{ turnstile.connect }}
              </span>
            </TableCell>
            <TableCell>
              <span :class="turnstile.reader_status === 'STOP' ? 'text-red-600' : 'text-green-600'">
                {{ turnstile.reader_status }}
              </span>
            </TableCell>

            <TableCell class="w-6">
              <IconCircleCheckFilled v-if="turnstile?.settings?.config_valid" class="text-green-500 mx-auto" />
              <IconCircleXFilled v-else class="text-red-500 mx-auto" />
            </TableCell>

            <TableCell class="w-6">
              <IconCircleCheckFilled v-if="turnstile?.settings?.reverse" class="text-green-500 mx-auto" />
              <IconCircleXFilled v-else class="text-red-500 mx-auto" />
            </TableCell>

            <TableCell>
              <Switch
                v-if="editingId === turnstile.sn"
                v-model="editingData.used"
              />
              <IconCircleCheckFilled v-else-if="turnstile.used" class="text-green-500" />
              <IconCircleXFilled v-else class="text-red-500" />
            </TableCell>

            <TableCell class="text-right flex gap-2 justify-end">
              <template v-if="editingId === turnstile.sn">
                <ShadCnButton size="sm" class="text-xs" @click="saveEdit">
                  {{ $t('turnstiles.save') }}
                </ShadCnButton>
                <ShadCnButton variant="outline" size="sm" class="text-xs" @click="cancelEdit">
                  {{ $t('turnstiles.cancel') }}
                </ShadCnButton>
              </template>
              <template v-else>
                <ShadCnButton size="sm" class="text-xs" @click="startEdit(turnstile)">
                  {{ $t('turnstiles.edit') }}
                </ShadCnButton>
                <ShadCnButton variant="outline" size="sm" class="text-xs" @click="openScannerSettings(turnstile)">
                  {{ $t('turnstiles.scanner-settings') }}
                </ShadCnButton>
              </template>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>

      <Pagination
        v-if="paginationMeta"
        :meta="paginationMeta"
        @page-change="fetchData"
      />
    </div>

    <PageLoader v-else-if="componentState === ComponentStateType.LOADING" :absolute-center="true" />

    <div v-else-if="componentState === ComponentStateType.ERROR" class="text-center text-red-500">
      {{ $t('misc.error') }}
    </div>

    <!-- Scanner Settings Modal -->
    <div v-if="scannerModal.isOpen" class="fixed inset-0 z-50 bg-black/65 grid place-items-center font-medium" @click.self="scannerModal.isOpen = false">
      <div class="max-w-[450px] w-full bg-white p-6 space-y-6 rounded-xl">
        <h2 class="text-2xl font-bold">{{ $t('turnstiles.scanner-settings') }}</h2>

        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <label>{{ $t('turnstiles.image-settings') }}</label>
            <Switch v-model="scannerModal.settings.reverse" />
          </div>

          <div class="flex items-center justify-between">
            <label>{{ $t('turnstiles.reverse-readers') }}</label>
            <Switch v-model="scannerModal.settings.readers_flip" />
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <h4 class="font-medium mb-2">{{ $t('turnstiles.reader-in') }}</h4>
              <div class="space-y-2">
                <div class="flex items-center justify-between">
                  <label>{{ $t('turnstiles.rfid') }}</label>
                  <Switch v-model="scannerModal.settings.readers_in.rfid" />
                </div>
                <div class="flex items-center justify-between">
                  <label>{{ $t('turnstiles.mifare') }}</label>
                  <Switch v-model="scannerModal.settings.readers_in.mifare" />
                </div>
                <div class="flex items-center justify-between">
                  <label>{{ $t('turnstiles.qr') }}</label>
                  <Switch v-model="scannerModal.settings.readers_in.qr" />
                </div>
              </div>
              <div class="pt-2">
                <Label class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32 mt-3">
                  {{ $t('products.turnstile-categories') }}
                </Label>
                <Select
                  v-model="scannerModal.default_categories"
                  :options="categories"
                  item-title="name"
                  item-value="id"
                  clearable
                  multiple
                />
              </div>
            </div>

            <div>
              <h4 class="font-medium mb-2">{{ $t('turnstiles.reader-out') }}</h4>
              <div class="space-y-2">
                <div class="flex items-center justify-between">
                  <label>{{ $t('turnstiles.rfid') }}</label>
                  <Switch v-model="scannerModal.settings.readers_out.rfid" />
                </div>
                <div class="flex items-center justify-between">
                  <label>{{ $t('turnstiles.mifare') }}</label>
                  <Switch v-model="scannerModal.settings.readers_out.mifare" />
                </div>
                <div class="flex items-center justify-between">
                  <label>{{ $t('turnstiles.qr') }}</label>
                  <Switch v-model="scannerModal.settings.readers_out.qr" />
                </div>
              </div>
              <div class="pt-2">
                <Label class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32 mt-3">
                  {{ $t('products.turnstile-categories') }}
                </Label>
                <Select
                  v-model="scannerModal.default_category_id_out"
                  :options="categories"
                  item-title="name"
                  item-value="id"
                  clearable
                />
              </div>
            </div>
          </div>

          <div class="flex justify-end gap-2 pt-2">
            <ShadCnButton variant="outline" @click="scannerModal.isOpen = false">
              {{ $t('turnstiles.cancel') }}
            </ShadCnButton>
            <ShadCnButton @click="saveScannerSettings">
              {{ $t('turnstiles.save') }}
            </ShadCnButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
