export const formDataFromObject = (obj: Record<any, any>): FormData => {
  const formData = new FormData();

  const appendFormData = (data: any, rootKey: string | null = null) => {
    if (data instanceof File) {
      formData.append(rootKey!, data);
    } else if (typeof data === 'object' && data !== null) {
      if (Array.isArray(data)) {
        data.forEach((item, index) => {
          appendFormData(item, `${rootKey}[${index}]`);
        });
      } else {
        Object.keys(data).forEach((key) => {
          const value = data[key];
          const formKey = rootKey ? `${rootKey}[${key}]` : key;
          appendFormData(value, formKey);
        });
      }
    } else if (data !== undefined && data !== null) {
      formData.append(rootKey!, data);
    }
  };

  appendFormData(obj);

  return formData;
};
