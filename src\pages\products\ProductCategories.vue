<script setup lang="ts">
import { PlusIcon } from 'lucide-vue-next';
import { reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import ProductCategoryDialog from '@/pages/products/components/dialogs/ProductCategoryDialog.vue';
import ProductCategoryGroup from '@/pages/products/ProductCategoryGroup.vue';
import Button from '@/shadcn-components/ui/button/Button.vue';
import adminApi from '@/util/adminAxios';
import { deployToast, ToastType } from '@/util/toast';
import type { Category, Response, Tree, MultiLang } from '@/util/types/api-responses';
import { ComponentStateType } from '@/util/types/components';

const { t } = useI18n();

const componentState = ref(ComponentStateType.LOADING);
const categoryTree = ref<Tree>([]);
const dialog = reactive({
  modalShown: false,
  data: undefined as unknown as { id?: string, label?: MultiLang, parent_id: string | undefined },
  isEdit: false,
});

const fetchData = async() => {
  componentState.value = ComponentStateType.LOADING;
  try {
    const { data } = await adminApi.get<Response<Tree>>('api/admin/products/categories/tree');
    categoryTree.value = data.data;
    componentState.value = ComponentStateType.OK;
  } catch {
    componentState.value = ComponentStateType.ERROR;
  }
};

await fetchData();

const onAddNewCategory = ({ id }: Category) => {
  dialog.modalShown = true;
  dialog.isEdit = false;
  dialog.data = { parent_id: id };
};

const onEditCategory = (category: { id: string, label: MultiLang, parent_id: string | undefined }) => {
  dialog.modalShown = true;
  dialog.data = category;
  dialog.isEdit = true;
};

const onDeleteCategory = async(id: string) => {
  try {
    await adminApi.delete(`api/admin/products/categories/${id}`);
    await fetchData();
  } catch {
    deployToast(ToastType.ERROR, {
      text: t('misc.error'),
      timeout: 6000,
    });
  }
};
</script>

<template>
  <div class="flex flex-col">
    <div class="flex items-center gap-4 mb-2 flex-wrap">
      <Button class="w-fit bg-gray-200 rounded-lg p-2 hover:bg-gray-300 transition-colors gap-0.5 flex items-center cursor-pointer" @click="onAddNewCategory">
        <PlusIcon class="aspect-square text-gray-500" />
        <span class="text-sm font-medium text-gray-600">{{ $t('products.new-category') }}</span>
      </Button>
    </div>

    <ProductCategoryGroup
      v-for="(treeChild, idx) in categoryTree"
      :key="idx"
      :tree-item="treeChild"
      :on-add-new-category="onAddNewCategory"
      :on-edit-category="onEditCategory"
      :on-delete-category="onDeleteCategory"
    />
  </div>

  <suspense v-if="dialog.modalShown">
    <ProductCategoryDialog v-model="dialog.modalShown" :is-edit="dialog.isEdit" :category-tree="categoryTree" :dialog-data="dialog.data" @on-create="fetchData" />
  </suspense>
</template>
