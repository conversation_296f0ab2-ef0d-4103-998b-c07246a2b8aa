import { Model as BaseModel } from 'vue-api-query';
import { TokenService } from '@/stores/auth-store';
import type { AxiosRequestConfig } from 'axios';

export default class Model extends BaseModel {
  baseURL() {
    return import.meta.env.VITE_APP_API_URL;
  }

  request(config: AxiosRequestConfig) {
    const token = TokenService.getAccessToken();

    if (!config.headers) {
      config.headers = {};
    }

    if (token) {
      config.headers!.Authorization = 'Bearer ' + token; // Set JWT token
    }
    return this.$http.request(config);
  }
}
