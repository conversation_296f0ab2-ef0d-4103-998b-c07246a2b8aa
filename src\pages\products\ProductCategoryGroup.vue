<script lang="ts" setup>
import { Trash2, Edit, PlusIcon } from 'lucide-vue-next';
import { computed } from 'vue';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/shadcn-components/ui/alert-dialog';
import { Button as ShadCnButton } from '@/shadcn-components/ui/button';
import { getFromMultiLangObject } from '@/util/multilang';

interface Props {
  treeItem: any,
  level?: number,
  onAddNewCategory: any,
  onEditCategory: any,
  onDeleteCategory: any,
  searchText?: string,
}

const props = withDefaults(defineProps<Props>(), {
  level: 0,
  searchText: '',
});

const contrastTextColor = computed(() => {
  const cleanedHex = props.treeItem.color.replace('#', '');

  const r = parseInt(cleanedHex.substring(0, 2), 16);
  const g = parseInt(cleanedHex.substring(2, 4), 16);
  const b = parseInt(cleanedHex.substring(4, 6), 16);

  return (0.299 * r + 0.587 * g + 0.114 * b) > 186 ? '!text-black' : '!text-white';
});
</script>

<template>
  <div class="rounded-t-md w-full max-w-full flex flex-col">
    <div class="w-full" :class="searchText ? 'mb-0.5' : 'mb-2'">
      <div class="p-2 flex items-center justify-between rounded-md w-full hover:bg-opacity-20" :class="contrastTextColor" :style="{ background: treeItem.color ?? 'oklch(87.2% 0.01 258.338)' }">
        <div class="flex w-full justify-between items-center gap-1">
          <div class="whitespace-nowrap overflow-auto sm:overflow-visible">
            <h2 class="w-fit font-bold">{{ getFromMultiLangObject(treeItem.label) }}</h2>
          </div>
          <div class="space-x-2">
            <ShadCnButton class="w-7 h-7 p-1.5 rounded-full" :title="$t('misc.edit')" variant="default" @click.stop.prevent="onAddNewCategory(treeItem)">
              <PlusIcon class="w-full h-full" />
            </ShadCnButton>
            <ShadCnButton class="w-7 h-7 p-1.5 rounded-full" :title="$t('misc.edit')" variant="default" @click.stop.prevent="onEditCategory(treeItem)">
              <Edit class="w-full h-full" />
            </ShadCnButton>
            <AlertDialog>
              <AlertDialogTrigger @click.prevent.stop>
                <ShadCnButton class="w-7 h-7 p-1.5 rounded-full bg-destructive hover:bg-destructive/90" :title="$t('global.remove')" variant="default">
                  <Trash2 class="w-full h-full" />
                </ShadCnButton>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>{{ $t('products.categories-delete-title', { name: getFromMultiLangObject(treeItem.label).value }) }}</AlertDialogTitle>
                  <AlertDialogDescription>{{ $t('misc.continue-prompt') }}</AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>{{ $t('misc.cancel') }}</AlertDialogCancel>
                  <AlertDialogAction class="bg-destructive hover:bg-destructive/90" @click="onDeleteCategory(treeItem.id)">{{ $t('misc.continue') }}</AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </div>
    </div>
    <div class="w-full max-w-full relative">
      <div v-for="(treeChild, idx) in treeItem.children" :key="idx" class="grid grid-cols-[1.25rem_1fr] sm:grid-cols-[3rem_1fr] items-center border-l border-gray-300">
        <div class="vertical-text text-[0.7rem] sm:text-sm w-fit justify-self-center" />
        <div class="w-full overflow-hidden">
          <ProductCategoryGroup
            class="max-w-full"
            :tree-item="treeChild"
            :level="level + 1"
            :on-add-new-category="onAddNewCategory"
            :on-edit-category="onEditCategory"
            :on-delete-category="onDeleteCategory"
          />
        </div>
      </div>
    </div>
  </div>
</template>
