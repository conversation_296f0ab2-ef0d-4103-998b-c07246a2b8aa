<script setup lang="ts">
import { IconLetterX, IconLetterZ, IconLoader2, IconRefresh } from '@tabler/icons-vue';
import { onUnmounted, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useCashRegisterStore } from '@/stores/cash-register';
import cashApi from '@/util/cashAxios';

type Action = 'zReport' | 'xReport';

const { t } = useI18n();
const store = useCashRegisterStore();

const emit = defineEmits(['close']);

const loader = ref<Action>();
const timeoutId = ref<ReturnType<typeof setTimeout>>();

const submitReport = async(action: Action) => {
  loader.value = action;
  timeoutId.value = setTimeout(() => {
    store.commandResponse = {
      state: 'ERROR',
      messages: [
        {
          message: t('cash-register.report-timeout'),
          code: 'TIMEOUT',
        },
      ],
    };
  }, 30 * 1000);

  try {
    await cashApi.post('/api/cash-register/commands', {
      data: {
        action,
      },
    });
  } catch {
    store.commandResponse = {
      state: 'ERROR',
      messages: [],
    };

    if (timeoutId.value) {
      clearTimeout(timeoutId.value);
    }
  }
};

const close = () => {
  if (!loader.value) {
    emit('close');
  }
};

const tryAgain = () => {
  store.commandResponse = undefined;
};

watch(() => store.commandResponse, res => {
  loader.value = undefined;
  if (timeoutId.value) {
    clearTimeout(timeoutId.value);
  }
  if (res && res.state === 'OK') {
    emit('close');
  }
}, { deep: true });

onUnmounted(() => {
  if (timeoutId.value) {
    clearTimeout(timeoutId.value);
  }

  store.commandResponse = undefined;
});
</script>

<template>
  <div class="fixed inset-0 z-50 bg-black/65 grid place-items-center" @click.self="close">
    <div class="max-w-[600px] w-full bg-white p-10 rounded-xl">
      <div v-if="!store.commandResponse">
        <h2 class="font-bold text-xl mb-4">{{ $t(`cash-register.report`) }}</h2>
        <div class="fig text-xl">
          <button class="w-full font-medium py-5 bg-orange-400 text-white rounded-md fig justify-center" @click="submitReport('zReport')">
            <IconLoader2 v-if="loader === 'zReport'" class="animate-spin" />
            <IconLetterZ v-else />
            {{ $t(`cash-register.z-report`) }}
          </button>
          <button class="w-full font-medium py-5 bg-blue-500 text-white rounded-md fig justify-center" @click="submitReport('xReport')">
            <IconLoader2 v-if="loader === 'xReport'" class="animate-spin" />
            <IconLetterX v-else />
            {{ $t(`cash-register.x-report`) }}
          </button>
        </div>
      </div>

      <div v-if="store.commandResponse && store.commandResponse?.state !== 'OK'">
        <div class="bg-slate-200 p-5 rounded-xl space-y-3">
          <div class="font-bold text-xl text-center text-red-500">{{ $t('cash-register.report-failed') }}</div>
          <div v-for="({ message, code }, index) in store.commandResponse?.messages" :key="index" class="flex gap-2 items-center">
            <span class="font-medium">{{ message }}</span>
            <span class="text-sm text-gray-500 mt-0.5">({{ code }})</span>
          </div>
        </div>
        <div class="mt-6 fig text-lg">
          <button class="w-full border py-3 font-medium rounded-xl" @click="close">
            <span>{{ $t('misc.close') }}</span>
          </button>
          <button class="w-full bg-blue-500 py-3 font-medium text-white rounded-xl fig justify-center" @click="tryAgain">
            <IconRefresh />
            <span>{{ $t('misc.try-again') }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
