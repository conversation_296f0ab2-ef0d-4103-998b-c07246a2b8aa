<script lang="ts" setup>
import { useEcho } from '@laravel/echo-vue';
import { onMounted, onUnmounted } from 'vue';
import { useRoute } from 'vue-router';
import { routeMap } from '@/router/routes';
import { useAuthStore } from '@/stores/auth-store';
import { initializeBasicBreadcrumbBehaviour } from '@/util/facades/breadcrumb';

const route = useRoute();
const authStore = useAuthStore();

initializeBasicBreadcrumbBehaviour(routeMap.home.meta.i18nTitle, routeMap.home.name, true, route);

const { listen, stopListening } = useEcho('dashboard.' + authStore?.user?.id, ['DashboardUserEvent'], (e) => {
  console.log(e);
});

onMounted(() => {
  listen();
  console.log('listening to channel started');
});

onUnmounted(() => {
  stopListening();
  console.log('listening to channel stopped');
});
</script>

<template>
  <div class="w-100" />
</template>
