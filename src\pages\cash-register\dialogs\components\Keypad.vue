<script setup lang="ts">
import { IconBackspace } from '@tabler/icons-vue';
import { vOnLongPress } from '@vueuse/components';
import { useVModel } from '@vueuse/core';

// eslint-disable-next-line
const emits = defineEmits<{
  (e: 'update:modelValue', payload: string[]): void;
  (e: 'addDigit', value: string): string;
  (e: 'deleteDigit'): void;
  (e: 'longPress'): void;
}>();

const props = defineProps<{
  defaultValue?: string[]
  modelValue?: string[]
  withDot?: boolean
  percent?: boolean
}>();

const modelValue = useVModel(props, 'modelValue', emits, {
  passive: true,
  defaultValue: props.defaultValue,
});

const keys = ['1', '2', '3', '4', '5', '6', '7', '8', '9'] as const;

const onLongPressCallbackDirective = () => {
  modelValue.value = [];
  emits('longPress');
};

const handleAddDot = () => {
  if (!modelValue.value?.includes('.')) {
    modelValue.value?.push('.');
  }
};

const addDigit = (digit: typeof keys[number] | '0') => {
  const wouldExceedPercent = props.percent && Number(modelValue.value?.join('') + digit) > 100;
  if (modelValue.value?.at(-3) === '.' || wouldExceedPercent) {
    return;
  }

  modelValue.value?.push(digit);
  emits('addDigit', digit);
};

const deleteDigit = () => {
  modelValue.value?.pop();
  emits('deleteDigit');
};
</script>

<template>
  <div class="grid grid-cols-3 gap-3 mx-auto">
    <button v-for="digit in keys" :key="digit" class="bg-white text-3xl font-medium h-[4.5rem] w-full rounded-lg active:scale-[0.99] border border-black/20" @click="addDigit(digit)">{{ digit }}</button>
    <button v-if="withDot" class="bg-white text-3xl font-medium h-[4.5rem] w-full rounded-xl active:scale-[0.99] border border-black/20" @click="handleAddDot">
      .
    </button>
    <div v-else />
    <button class="bg-white text-3xl font-medium h-[4.5rem] w-full rounded-xl active:scale-[0.99] border border-black/20" @click="addDigit('0')">
      0
    </button>
    <button v-on-long-press="[onLongPressCallbackDirective, { delay: 500 }]" class="bg-white font-medium h-[4.5rem] w-full rounded-xl active:scale-[0.99] border border-black/20 grid place-items-center" @click="deleteDigit">
      <IconBackspace size="30" class="mr-0.5 text-rose-900" />
    </button>
  </div>
</template>
