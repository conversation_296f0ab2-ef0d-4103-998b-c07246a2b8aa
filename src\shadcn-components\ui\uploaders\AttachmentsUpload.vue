<script setup lang="ts">
import { IconTrash } from '@tabler/icons-vue';
import { GripVertical, Upload } from 'lucide-vue-next';
import { ref, computed } from 'vue';
import { Container, Draggable } from 'vue-dndrop';
import { useI18n } from 'vue-i18n';
import PageLoader from '@/components/global/PageLoader.vue';
import ImagePreview from '@/pages/products/components/ImagePreview.vue';
import Button from '@/shadcn-components/ui/button/Button.vue';
import Select from '@/shadcn-components/ui/inputs/Select.vue';
import Textarea from '@/shadcn-components/ui/inputs/Textarea.vue';
import { TableCell } from '@/shadcn-components/ui/table';
import { useLightBoxStore } from '@/stores/light-box-store';
import adminApi from '@/util/adminAxios';
import { applyDrag } from '@/util/dnd';
import { deployToast, ToastType } from '@/util/toast';
import type { Attachment, AttachmentType } from '@/util/types/api-responses';
const lightBoxStore = useLightBoxStore();

const { t } = useI18n();

const emit = defineEmits(['getAttachments']);
const props = defineProps<{
  types?: AttachmentType[];
  errors?: Record<string, string[]>;
  attachments: Attachment[],
  readOnly?: boolean,
}>();

const fileLoaderIdx = ref(-1);
const attchmnts = ref<Attachment[]>(props.attachments?.map((attchmnt: Attachment) => ({
  ...attchmnt,
  type_id: attchmnt.type?.id,
})));

const addNewGroup = () => {
  attchmnts.value.push({
    type_id: null,
    note: '',
    items: [],
  });
};

const removeGroup = (idx: number) => {
  attchmnts.value.splice(idx, 1);
};

const sendImages = async(files: FileList, idx: number) => {
  try {
    for (const file of files) {
      const to = setTimeout(() => {
        fileLoaderIdx.value = idx;
      }, 250);

      const formData = new FormData();
      formData.append('file', file);
      formData.append('public_url', '1');

      const { data } = await adminApi.post('/media/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      clearTimeout(to);
      fileLoaderIdx.value = -1;

      attchmnts.value[idx].items.push({
        filename: data.files[0].path,
        file_original_name: data.files[0].name,
        mime_type: data.files[0].mime,
        url_thumb: data.files[0].public_url,
        url: data.files[0].public_url,
      });
    }
  } catch {
    deployToast(ToastType.ERROR, {
      text: t('misc.file-upload-error'),
      timeout: 3000,
    });

    fileLoaderIdx.value = -1;
  }
};

const onImageDrop = async(e: DragEvent, idx: number) => {
  e.preventDefault();
  e.stopPropagation();
  const files = e.dataTransfer?.files;

  if (!files) {
    return;
  }

  await sendImages(files, idx);
};

const onImageInput = async(e: Event, idx: number) => {
  const files = (e.target as HTMLInputElement).files;
  if (!files) {
    return;
  }

  await sendImages(files, idx);
};

const onDndDrop = (dropResult: any) => {
  // @ts-ignore
  attchmnts.value = applyDrag(attchmnts.value, dropResult);
};

const openLightBox = (idxAttach: number, idxItems: number) => {
  lightBoxStore.images = attchmnts.value[idxAttach].items?.map(i => i.url) as unknown as string[];
  lightBoxStore.index = idxItems;
  lightBoxStore.visible = true;
};

emit('getAttachments', attchmnts.value);

const typeErrors = computed(() => {
  if (!props.errors) {
    return [];
  }

  const errorMap = [];
  Object.entries(props.errors).forEach(([key, value]) => {
    if (key.includes('attachments')) {
      errorMap[key.split('.')[1]] = value;
    }
  });
  return errorMap;
});
</script>

<template>
  <div class="w-full caption-bottom text-sm relative overflow-auto">
    <div class="w-full grid grid-cols-4 border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
      <div class="hidden md:flex col-span-2 h-12 px-4 items-center font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0">{{ $t('products.files') }}</div>
      <div class="hidden md:flex h-12 px-4 items-center font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0">{{ $t('misc.type') }}</div>
      <div class="hidden md:flex h-12 px-4 items-center font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0">{{ $t('products.note') }}</div>
    </div>
    <Container
      lock-axis="y"
      class="w-full [&_tr:last-child]:border-0"
      drag-handle-selector=".column-drag-handle"
      @drop="onDndDrop"
    >
      <Draggable
        v-for="(attchmnt, idx) in attchmnts"
        :key="attchmnt.id"
        :class="[readOnly ? 'pl-0' : 'pl-7', '!w-full !grid !grid-cols-1 md:!grid-cols-4 border-b transition-colors relative hover:bg-muted/50 data-[state=selected]:bg-muted']"
      >
        <div v-if="!readOnly" class="flex flex-col gap-3 absolute top-3.5 left-2 w-fit">
          <GripVertical class="column-drag-handle text-gray-500 hover:text-gray-700 transition-colors cursor-grab active:cursor-grabbing" />
          <button @click.prevent="removeGroup(idx)">
            <IconTrash class="text-gray-500 hover:text-rose-500 transition-colors" />
          </button>
        </div>
        <TableCell class="md:col-span-2 flex flex-wrap items-start gap-2 px-4 md:p-4">
          <ImagePreview v-for="(item, i) in attchmnt.items" :key="item.id" :read-only :name="item.file_original_name" :thumb="item.url_thumb" :url="item.url" :mime-type="item.mime_type" @remove-item="attchmnt.items.splice(i, 1)" @open-light-box="openLightBox(idx, i)" />
          <div v-if="!readOnly" class="group text-gray-50 border-dashed border-gray-300 hover:border-gray-200 transition-colors border-[3px] rounded-lg size-24 relative">
            <input id="file" multiple :class="[fileLoaderIdx === idx ? 'cursor-wait' : 'cursor-pointer', 'hide-file-input w-full h-full']" type="file" @input="e => onImageInput(e, idx)" @drop="e => onImageDrop(e, idx)">
            <PageLoader v-if="fileLoaderIdx === idx" class="absolute-center pointer-events-none opacity-70" />
            <Upload v-else :size="30" class="absolute-center text-gray-300 group-hover:text-gray-200 transition-colors pointer-events-none" />
          </div>
        </TableCell>
        <TableCell class="p-0 md:p-4">
          <Select
            v-model="attchmnt.type_id"
            :options="types"
            :disabled="readOnly"
            :errors="typeErrors[idx]"
            item-value="id"
            item-title="name"
            multilang
            class="w-full"
          />
        </TableCell>
        <TableCell class="px-0 md:p-4">
          <Textarea
            v-model="attchmnt.note"
            :disabled="readOnly"
          />
        </TableCell>
      </Draggable>
    </Container>

    <div v-if="!readOnly" class="flex justify-center">
      <Button variant="outline" class="mt-3" @click.prevent="addNewGroup">{{ $t('misc.add-new') }}</Button>
    </div>
  </div>
</template>
