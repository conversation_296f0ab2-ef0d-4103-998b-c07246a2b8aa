<script setup lang="ts">
import { IconLoader2, IconCircle<PERSON>heck, IconExclamationCircle } from '@tabler/icons-vue';
import { ref, onBeforeUnmount, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useCashRegisterStore } from '@/stores/cash-register';
import cashApi from '@/util/cashAxios';
import { CustomEvents } from '@/util/facades/custom-event';
import { FakeReaderActions, generateFakeCardReadFn } from '@/util/fakers/scanner';
import { deployToast, ToastType } from '@/util/toast';
import { AndroidActions } from '../androidRequestHandler';

interface CardResponseData {
  referenceNumber: string
}

interface TicketData {
  id: string
  reference_number: string
  scan_type: number
  used_at: string
  created_at: string
  review_at: string
  category: any
  order_items: Array<{
    id: string
    product: {
      id: string
      type: string
      name: string
      unit_id: {
        id: number
        name: string
        shortcut: string
      }
      price: number
      tax_id: {
        id: number
        name: string
        code: string
        rate: number
        created_at: string
        updated_at: string
      }
      ticket: {
        time_ticket: boolean
        ticket_time: number
        additional_time: number
        one_way_ticket: boolean
        scan_type: number
        require_return: boolean
        reserved_daytime_time_from: string | null
        reserved_daytime_time_to: string | null
        turnstile_ticket_categories: Array<{
          id: string
          name: string
          zone_level: number
        }>
      }
    }
    name: string
    type: string
    price: number
    price_unit: number
    price_calculated: number
    discount_calculated: number
    unit: string
    vat_rate: number
    vat_rate_code: string
    quantity: number
    created_at: string
    updated_at: string
    tickets_count: number
  }>
  tracker: Record<string, {
    tracked: number
    entries: number
    paid: number
    paid_entries: number
    unlimited_entries: boolean
    reserve: number
    unlimited_time: boolean
    need_resolve: string | null
    entry_difference: number
    time_difference: number
    category: {
      id: string
      name: string
      zone_level: number
    }
  }>
}

const emit = defineEmits(['close']);
const { t } = useI18n();

const store = useCashRegisterStore();
const loading = ref(false);
const cardLoading = ref(false);
const ticketData = ref<TicketData | null>(null);
const showResetConfirmation = ref(false);
const errors = ref({
  cardRead: false,
  ticketDetail: false,
  resolveTicket: false,
});

const formatTime = (seconds: number): string => {
  const hours = Math.floor(Math.abs(seconds) / 3600);
  const minutes = Math.floor((Math.abs(seconds) % 3600) / 60);
  const secs = Math.abs(seconds) % 60;

  return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

const fakeRead = generateFakeCardReadFn(FakeReaderActions.DATA_FROM_READER, '3107453726');

const delayedCloseModal = () => {
  setTimeout(() => {
    emit('close');
  }, 5000);
};

const createOnRegisterCommandHandler = (resolve: (value?: CardResponseData | PromiseLike<CardResponseData>) => void) => {
  return (e: Event) => {
    const { detail } = e as CustomEvent<{ action: string, data: string }>;

    if (detail.action !== AndroidActions.DATA_FROM_READER) {
      return;
    }

    resolve({ referenceNumber: detail.data });
    return;
  };
};

const fetchTicketDetail = async(reference: string) => {
  loading.value = true;
  try {
    const { data } = await cashApi.get(`/api/cash-register/ticket/${reference}`);
    ticketData.value = data.data;
  } catch (error) {
    errors.value.ticketDetail = true;
    console.error('Error fetching ticket detail:', error);
    deployToast(ToastType.ERROR, {
      text: t('cash-register.error-fetching-ticket-detail'),
      timeout: 6000,
    });
    ticketData.value = null;
    delayedCloseModal();
  } finally {
    loading.value = false;
  }
};

const loadFromCard = async() => {
  cardLoading.value = true;
  ticketData.value = null;

  try {
    const { promise: cardReadPromise, resolve, reject } = Promise.withResolvers<CardResponseData | undefined>();
    const handler = createOnRegisterCommandHandler(resolve);
    window.eventBus.addEventListener(CustomEvents.DataFromReaderEvent, handler);
    const timeoutId = setTimeout(() => {
      reject('Timeout');
    }, 15000);
    onBeforeUnmount(() => {
      window.eventBus.removeEventListener(CustomEvents.DataFromReaderEvent, handler);
      clearTimeout(timeoutId);
    });

    if (import.meta.env.DEV) {
      setTimeout(() => {
        fakeRead();
      }, 3000);
    }

    const cardData = await cardReadPromise;
    clearTimeout(timeoutId);
    window.eventBus.removeEventListener(CustomEvents.DataFromReaderEvent, handler);

    if (cardData?.referenceNumber) {
      await fetchTicketDetail(cardData.referenceNumber);
    }
  } catch (error: any) {
    console.error('Error reading card:', error);
    deployToast(ToastType.ERROR, {
      text: t('cash-register.error-fetching-ticket-from-reader'),
      timeout: 6000,
    });
    errors.value.cardRead = true;
    delayedCloseModal();
  } finally {
    cardLoading.value = false;
  }
};

const hasIssues = (): boolean => {
  if (!ticketData.value?.tracker) {
    return false;
  }

  return Object.values(ticketData.value.tracker).some(tracker => tracker.need_resolve !== null);
};

const resolveTicket = async() => {
  try {
    const { data } = await cashApi.post(`/api/cash-register/ticket/${ticketData.value!.reference_number}/resolve`);
    store.order = data.data;
    await store.loadCart();
    emit('close');
  } catch (error) {
    deployToast(ToastType.ERROR, {
      text: t('cash-register.error-resolving-ticket'),
      timeout: 6000,
    });
    console.error('Error resolving ticket:', error);
    errors.value.resolveTicket = true;
  } finally {
    loading.value = false;
  }
};

const showResetDialog = () => {
  showResetConfirmation.value = true;
};

const confirmReset = async() => {
  try {
    await cashApi.patch(`/api/cash-register/ticket/${ticketData.value?.reference_number}/return`);

    deployToast(ToastType.SUCCESS, {
      text: t('cash-register.bracelet-reset-successful'),
      timeout: 6000,
    });

    showResetConfirmation.value = false;
    emit('close');
  } catch (error) {
    deployToast(ToastType.ERROR, {
      text: t('cash-register.bracelet-reset-failed'),
      timeout: 6000,
    });
    console.error('Error resetting bracelet:', error);
  }
};

const cancelReset = () => {
  showResetConfirmation.value = false;
};

onMounted(() => {
  loadFromCard();
});
</script>

<template>
  <div class="fixed inset-0 z-50 bg-black/65 grid place-items-center" @click.self="emit('close')">
    <div class="max-w-[600px] w-full bg-white p-10 rounded-xl max-h-[90vh] overflow-y-auto">
      <div class="space-y-4">
        <h2 class="text-xl font-bold">{{ $t('cash-register.surcharge') }}</h2>

        <!-- <div class="flex gap-4">
          <button
            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors flex items-center gap-2 flex-1"
            :disabled="cardLoading"
            @click="loadFromCard"
          >
            <IconLoader2 v-if="cardLoading" class="animate-spin h-4 w-4" />
            <IconCreditCard v-else class="h-4 w-4" />
            {{ $t('cash-register.scan-ticket') }}
          </button>
        </div> -->

        <div v-if="loading || cardLoading" class="flex justify-center py-2">
          <IconLoader2 class="animate-spin h-8 w-8 text-gray-400" />
        </div>

        <div v-else-if="ticketData" class="space-y-4">
          <!-- Global Status -->
          <div class="p-4 rounded-lg border" :class="hasIssues() ? 'bg-red-50 border-red-200' : 'bg-green-50 border-green-200'">
            <div class="flex items-center gap-2">
              <IconExclamationCircle v-if="hasIssues()" class="h-5 w-5 text-red-600" />
              <IconCircleCheck v-else class="h-5 w-5 text-green-600" />
              <span class="font-medium" :class="hasIssues() ? 'text-red-800' : 'text-green-800'">
                {{ hasIssues() ? $t('cash-register.surcharge-required') : $t('cash-register.ticket-valid') }}
              </span>
            </div>
          </div>

          <!-- Ticket Info -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h3 class="font-medium text-gray-900 mb-2">{{ $t('cash-register.ticket-info') }}</h3>
            <div class="text-sm text-gray-600 space-y-1">
              <div><strong>{{ $t('cash-register.reference') }}:</strong> {{ ticketData.reference_number }}</div>
              <div><strong>{{ $t('cash-register.created-at') }}:</strong> {{ new Date(ticketData.created_at).toLocaleString() }}</div>
              <div v-if="ticketData.used_at"><strong>{{ $t('cash-register.used-at') }}:</strong> {{ new Date(ticketData.used_at).toLocaleString() }}</div>
            </div>
          </div>

          <!-- Tracker Details -->
          <div v-for="(tracker, trackerId) in ticketData.tracker" :key="trackerId" class="border rounded-lg p-4">
            <h3 class="font-medium text-gray-900 mb-3">{{ tracker.category.name }}</h3>

            <div v-if="tracker.need_resolve === null">
              <div class="text-green-600 flex items-center gap-2">
                <IconCircleCheck class="h-4 w-4" />
                <span>{{ $t('cash-register.no-issues') }}</span>
              </div>
              <div class="text-sm text-gray-600 space-y-1 mt-2">
                <div>
                  <strong>{{ $t('cash-register.extra-entries') }}:</strong> {{ Math.abs(tracker.entry_difference) }}
                </div>
                <div>
                  <strong>{{ $t('cash-register.remaining-time') }}:</strong> {{ formatTime(tracker.time_difference + tracker.reserve) }}
                </div>
                <div>
                  <strong>{{ $t('cash-register.total-tracked') }}:</strong> {{ formatTime(tracker.tracked) }}
                </div>
                <div>
                  <strong>{{ $t('cash-register.paid-time') }}:</strong> {{ formatTime(tracker.paid) }}
                </div>
              </div>
            </div>

            <div v-else class="space-y-2">
              <div class="text-red-600 flex items-center gap-2">
                <IconExclamationCircle class="h-4 w-4" />
                <span>{{ $t('cash-register.issue-type') }}: {{ tracker.need_resolve }}</span>
              </div>

              <div class="text-sm text-gray-600 space-y-1">
                <div v-if="tracker.entry_difference < 0">
                  <strong>{{ $t('cash-register.extra-entries') }}:</strong> {{ Math.abs(tracker.entry_difference) }}
                </div>
                <div v-if="tracker.time_difference < 0">
                  <strong>{{ $t('cash-register.extra-time') }}:</strong> {{ formatTime(tracker.time_difference) }}
                </div>
                <div>
                  <strong>{{ $t('cash-register.total-tracked') }}:</strong> {{ formatTime(tracker.tracked) }}
                </div>
                <div>
                  <strong>{{ $t('cash-register.paid-time') }}:</strong> {{ formatTime(tracker.paid) }}
                </div>
              </div>
            </div>
          </div>

          <button
            v-if="!hasIssues()"
            class="w-fit px-4 bg-gray-200 text-gray-700 border border-gray-300 py-3 rounded-lg font-medium hover:bg-gray-300 transition-colors mt-4"
            @click="emit('close')"
          >
            {{ $t('misc.close') }}
          </button>

          <!-- Payment Button -->
          <div v-if="hasIssues()" class="sticky bottom-0 bg-white pt-4 border-t space-y-3">
            <button
              class="w-full bg-red-500 text-white py-3 rounded-lg font-medium hover:bg-red-600 transition-colors"
              @click="resolveTicket"
            >
              {{ $t('cash-register.process-surcharge') }}
            </button>
            <button
              class="w-full bg-orange-500 text-white py-3 rounded-lg font-medium hover:bg-orange-600 transition-colors"
              @click="showResetDialog"
            >
              {{ $t('cash-register.reset-bracelet') }}
            </button>
          </div>
          <div v-else class="sticky bottom-0 bg-white pt-4 border-t space-y-3">
            <button
              class="w-full bg-orange-500 text-white py-3 rounded-lg font-medium hover:bg-orange-600 transition-colors"
              @click="showResetDialog"
            >
              {{ $t('cash-register.reset-bracelet') }}
            </button>
          </div>
        </div>

        <!-- No Results -->
        <div v-if="!ticketData && !Object.values(errors).some(Boolean)" class="text-center text-gray-400 italic">
          {{ $t('cash-register.scan-ticket-to-start') }}
        </div>

        <div v-if="Object.values(errors).some(Boolean)" class="mt-4">
          <div>
            <div class="bg-rose-50 border border-rose-200 p-5 rounded-xl space-y-3 text-lg text-center">
              <div v-if="errors.ticketDetail" class="font-bold text-2xl text-red-500">{{ $t('cash-register.error-fetching-ticket-detail') }}</div>
              <div v-if="errors.cardRead" class="font-bold text-2xl text-red-500">{{ $t('cash-register.error-fetching-ticket-from-reader') }}</div>
              <div v-if="errors.resolveTicket" class="font-bold text-2xl text-red-500">{{ $t('cash-register.error-resolving-ticket') }}</div>
            </div>
          </div>

          <button
            class="w-fit px-4 bg-gray-200 text-gray-700 border border-gray-300 py-3 rounded-lg font-medium hover:bg-gray-300 transition-colors mt-4"
            @click="emit('close')"
          >
            {{ $t('misc.close') }}
          </button>
        </div>
      </div>
    </div>

    <!-- Reset Confirmation Dialog -->
    <div v-if="showResetConfirmation" class="fixed inset-0 z-60 bg-black/75 grid place-items-center">
      <div class="max-w-[400px] w-full bg-white p-8 rounded-xl">
        <div class="space-y-4">
          <h3 class="text-lg font-bold text-center">{{ $t('cash-register.confirm-bracelet-reset') }}</h3>
          <p class="text-gray-600 text-center">{{ $t('cash-register.bracelet-reset-warning') }}</p>
          <div class="flex gap-4">
            <button
              class="flex-1 bg-gray-500 text-white py-3 rounded-lg font-medium hover:bg-gray-600 transition-colors"
              @click="cancelReset"
            >
              {{ $t('misc.cancel') }}
            </button>
            <button
              class="flex-1 bg-red-500 text-white py-3 rounded-lg font-medium hover:bg-red-600 transition-colors"
              @click="confirmReset"
            >
              {{ $t('misc.yes') }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
