<script setup lang="ts">
import { useRouteQuery } from '@vueuse/router';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-vue-next';
import { PaginationEllipsis, PaginationFirst, PaginationLast, PaginationList, PaginationListItem, PaginationNext, PaginationPrev, PaginationRoot } from 'radix-vue';
import { computed, ref } from 'vue';
import type { Meta } from '@/util/types/api-responses';

const emit = defineEmits(['newPage']);

const props = defineProps<{
  meta: Meta
  inCashRegister?: boolean
}>();

const queryPage = useRouteQuery('page');
const queryLimit = useRouteQuery('limit');
const currentPage = ref(props.inCashRegister ? 1 : queryPage.value ? Number(queryPage.value) : 1);
const currentLimit = ref(props.inCashRegister ? 15 : queryLimit.value ? Number(queryLimit.value) : 15);

const onPageChange = () => {
  if (!props.inCashRegister) {
    queryPage.value = currentPage.value as unknown as string;
    queryLimit.value = currentLimit.value as unknown as string;
  }
  emit('newPage', { page: currentPage.value, limit: currentLimit.value });
};

const isMobile = computed(() => window.matchMedia('(max-width: 768px)').matches);
</script>

<template>
  <div class="w-fit mx-auto mt-4 flex items-center gap-4">
    <el-select
      v-if="!inCashRegister"
      v-model="currentLimit"
      placeholder="Select"
      size="large"
      class="w-[80px]"
      @update:model-value="onPageChange"
    >
      <el-option
        v-for="item in [15, 30, 60, 100]"
        :key="item"
        :label="item"
        :value="item"
      />
    </el-select>
    <PaginationRoot
      v-model:page="currentPage"
      :total="meta?.total"
      :sibling-count="inCashRegister || isMobile ? 0 : 3"
      :items-per-page="meta?.per_page"
      show-edges
      :default-page="Number(queryPage) ?? 1"
      @update:page="onPageChange"
    >
      <PaginationList
        v-slot="{ items }"
        class="flex items-center gap-1 text-gray-700"
      >
        <PaginationFirst class="w-9 h-9 text-paynes-gray-500 hover:text-paynes-gray-600 hidden sm:flex items-center justify-center disabled:opacity-50 disabled:!text-paynes-gray-500 focus-within:outline focus-within:outline-1 focus-within:outline-offset-1 rounded">
          <ChevronsLeft />
        </PaginationFirst>
        <PaginationPrev class="w-9 h-9 text-paynes-gray-500 hover:text-paynes-gray-600 flex items-center justify-center mr-4 disabled:opacity-50 disabled:!text-paynes-gray-500 focus-within:outline focus-within:outline-1 focus-within:outline-offset-1 rounded">
          <ChevronLeft />
        </PaginationPrev>
        <template v-for="(page, index) in items">
          <PaginationListItem
            v-if="page.type === 'page'"
            :key="index"
            class="w-9 h-9 rounded bg-gray-300 data-[selected]:bg-paynes-gray-500 data-[selected]:text-white hover:bg-gray-200 transition focus-within:outline focus-within:outline-1 focus-within:outline-offset-1"
            :value="page.value"
          >
            {{ page.value }}
          </PaginationListItem>
          <PaginationEllipsis
            v-else
            :key="page.type"
            :index="index"
            class="w-9 h-9 text-paynes-gray-500 items-center justify-center hidden sm:flex"
          >
            &#8230;
          </PaginationEllipsis>
        </template>
        <PaginationNext class="w-9 h-9 text-paynes-gray-500 hover:text-paynes-gray-600 flex items-center justify-center ml-4 disabled:opacity-50 disabled:!text-paynes-gray-500 focus-within:outline focus-within:outline-1 focus-within:outline-offset-1 rounded">
          <ChevronRight />
        </PaginationNext>
        <PaginationLast class="w-9 h-9 text-paynes-gray-500 hover:text-paynes-gray-600 hidden sm:flex items-center justify-center disabled:opacity-50 disabled:!text-paynes-gray-500 focus-within:outline focus-within:outline-1 focus-within:outline-offset-1 rounded">
          <ChevronsRight />
        </PaginationLast>
      </PaginationList>
    </PaginationRoot>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-select--large .el-select__wrapper) {
  min-height: 36px !important;
  background: #d1d5db !important;
}
</style>
