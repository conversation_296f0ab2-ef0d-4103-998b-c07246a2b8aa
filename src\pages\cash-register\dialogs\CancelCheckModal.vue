<script setup lang="ts">
import { useCashRegisterStore } from '@/stores/cash-register';
import cashApi from '@/util/cashAxios';

const emit = defineEmits(['close']);

const store = useCashRegisterStore();

const cancelOrder = async() => {
  await cashApi.put(`/api/cash-register/orders/${store.order!.id}`, {
    state: 'CANCELLED',
  });
  store.clearCart();
  emit('close');
};
</script>

<template>
  <div class="fixed inset-0 z-50 bg-black/65 grid place-items-center" @click.self="emit('close')">
    <div class="max-w-[600px] w-full bg-white p-10 rounded-xl">
      <h2 class="font-bold text-xl mb-4">{{ $t(`cash-register.confirm-cancel`) }}</h2>
      <div class="fig text-xl">
        <button class="w-full font-medium py-5 border rounded-md fig justify-center" @click="emit('close')">
          {{ $t(`misc.cancel`) }}
        </button>
        <button class="w-full font-medium py-5 bg-red-500 text-white rounded-md fig justify-center" @click="cancelOrder">
          {{ $t(`misc.confirm`) }}
        </button>
      </div>
    </div>
  </div>
</template>
