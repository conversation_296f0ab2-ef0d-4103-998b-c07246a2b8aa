<script setup lang="ts">
import { GridStack, type GridStackWidget } from 'gridstack';
import { onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';
import AdminSkeleton from '@/pages/cr/components/AdminSkeleton.vue';
import CategoriesSkeleton from '@/pages/cr/components/CategoriesSkeleton.vue';
import KeypadSkeleton from '@/pages/cr/components/KeypadSkeleton.vue';
import ListSkeleton from '@/pages/cr/components/ListSkeleton.vue';
import { Button } from '@/shadcn-components/ui/button';
import adminApi from '@/util/adminAxios';
import { deployToast, ToastType } from '@/util/toast';
import type { CR, CRComponent, CRLayout } from '@/util/types/api-responses';

const route = useRoute();
const { t } = useI18n();

let grid: GridStack | null = null;
const layout = ref<CRLayout>();
const components = ref<CRComponent[]>([]);
const editorRef = ref<HTMLDivElement>();
const editorHeight = ref(0);
const cashRegister = ref<CR>();

const getEditorHeight = (resolution = [1920, 1024]) => {
  let editorWidth = 0;
  if (editorRef.value) {
    editorWidth = editorRef.value.getBoundingClientRect().width;
  }

  const gcd = (a: number, b: number) => b ? gcd(b, a % b) : a;

  const [w0, h0] = resolution;
  const g = gcd(w0, h0);

  editorHeight.value = Math.round((editorWidth * (h0 / g)) / (w0 / g));
};

const skeletonMap = {
  'keypad': KeypadSkeleton,
  'list': ListSkeleton,
  'categories': CategoriesSkeleton,
  'admin': AdminSkeleton,
};

const saveLayout = async() => {
  try {
    await adminApi.put(`api/admin/cash-registers/${route.params.id}`, {
      ...cashRegister.value,
      cash_register_groups: cashRegister.value?.cash_register_groups?.map(g => g.id),
      layout: layout.value,
    });
  } catch {
    deployToast(ToastType.ERROR, {
      text: t('misc.error'),
      timeout: 6000,
    });
  }
};

onMounted(async() => {
  const { data: { data }} = await adminApi.get(`api/admin/cash-registers/${route.params.id}`);
  cashRegister.value = data;
  layout.value = data.layout;
  components.value = data.layout?.components;

  setTimeout(() => {
    grid = GridStack.init({
      float: true,
      cellHeight: editorHeight.value / 12,
      row: 12,
      minRow: 12,
      maxRow: 12,
      acceptWidgets: true,
    });

    const getLayout = () => {
      const widgets = grid?.save() as GridStackWidget[];
      layout.value = {
        components: widgets.map(w => ({
          type: w.id!,
          x: w.x!,
          y: w.y!,
          w: w.w ?? 1,
          h: w.h ?? 1,
        })),
      };
    };

    GridStack.setupDragIn('.pool .grid-stack-item', { appendTo: 'body' });
    grid.on('added', getLayout);
    grid.on('change', getLayout);
  });

  getEditorHeight();
});
</script>

<template>
  <header class="pool fig pb-4">
    <KeypadSkeleton />
    <ListSkeleton />
    <CategoriesSkeleton />
    <AdminSkeleton />
  </header>

  <div ref="editorRef" class="grid-stack border-2 border-dashed border-black" :style="{ height: editorHeight }">
    <component :is="skeletonMap[comp.type]" v-for="comp in components" :key="comp.type" :gs-x="comp.x" :gs-y="comp.y" :gs-h="comp.h" :gs-w="comp.w" />
  </div>

  <footer class="mt-4 flex justify-end">
    <Button @click="saveLayout">Save</Button>
  </footer>
</template>
