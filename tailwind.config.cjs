import containerQueries from '@tailwindcss/container-queries';
import animate from 'tailwindcss-animate';

/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ['class'],
  safelist: ['dark'],
  prefix: '',

  content: [
    './pages/**/*.{ts,tsx,vue}',
    './components/**/*.{ts,tsx,vue}',
    './app/**/*.{ts,tsx,vue}',
    './src/**/*.{ts,tsx,vue}',
  ],

  theme: {
    fontFamily: {
      sans: ['Roboto', 'sans-serif'],
    },
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    screens: {
      lw: '390px',
      sm: '640px',
      md: '768px',
      lg: '1024px',
      xl: '1280px',
      '2xl': '1440px',
      '3xl': '1700px',
      '4xl': '1900px',
      '5xl': '2200px',
    },
    extend: {
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        'paynes-gray': {
          '100': '#0c1316',
          '200': '#17272c',
          '300': '#233a42',
          '400': '#2e4d58',
          '500': '#3a606e',
          '600': '#52879b',
          '700': '#79a7b8',
          '800': '#a5c5d0',
          '900': '#d2e2e7',
          DEFAULT: '#3a606e',
        },
        'columbia-blue': {
          '100': '#0d2c48',
          '200': '#1b5891',
          '300': '#2983d8',
          '400': '#72ade5',
          '500': '#b9d6f2',
          '600': '#c8dff5',
          '700': '#d6e7f7',
          '800': '#e3effa',
          '900': '#f1f7fc',
          DEFAULT: '#b9d6f2',
        },
        licorice: {
          '100': '#050002',
          '200': '#090105',
          '300': '#0e0107',
          '400': '#130209',
          '500': '#18020c',
          '600': '#710938',
          '700': '#cb1064',
          '800': '#f04c96',
          '900': '#f8a5ca',
          DEFAULT: '#18020c',
        },
        mustard: {
          '100': '#473500',
          '200': '#8e6b01',
          '300': '#d5a001',
          '400': '#fec620',
          '500': '#fed766',
          '600': '#fee085',
          '700': '#ffe8a4',
          '800': '#fff0c2',
          '900': '#fff7e1',
          DEFAULT: '#fed766',
        },
        'fire-engine-red': {
          '100': '#270809',
          '200': '#4d1012',
          '300': '#74191c',
          '400': '#9b2125',
          '500': '#c1292e',
          '600': '#d8494e',
          '700': '#e2777a',
          '800': '#eca4a7',
          '900': '#f5d2d3',
          DEFAULT: '#c1292e',
        },
        platinum: {
          '100': '#2d2d2d',
          '200': '#5a5a5a',
          '300': '#878787',
          '400': '#b4b4b4',
          '500': '#e0e0e0',
          '600': '#e7e7e7',
          '700': '#ededed',
          '800': '#f3f3f3',
          '900': '#f9f9f9',
          DEFAULT: '#e0e0e0',
        },
        department: {
          DEFAULT: '#2E4D58',
          over: '#52879b',
        },
        division: {
          DEFAULT: '#823038',
          over: '#c65464',
        },
        team: {
          DEFAULT: '#52822c',
          over: '#94bd76',
        },
        sidebar: {
          DEFAULT: 'hsl(var(--sidebar-background))',
          foreground: 'hsl(var(--sidebar-foreground))',
          primary: 'hsl(var(--sidebar-primary))',
          'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
          accent: 'hsl(var(--sidebar-accent))',
          'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
          border: 'hsl(var(--sidebar-border))',
          ring: 'hsl(var(--sidebar-ring))',
        },
      },
      borderRadius: {
        xl: 'calc(var(--radius) + 4px)',
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      keyframes: {
        'accordion-down': {
          from: {
            height: 0,
          },
          to: {
            height: 'var(--radix-accordion-content-height)',
          },
        },
        'accordion-up': {
          from: {
            height: 'var(--radix-accordion-content-height)',
          },
          to: {
            height: 0,
          },
        },
        'collapsible-down': {
          from: {
            height: 0,
          },
          to: {
            height: 'var(--radix-collapsible-content-height)',
          },
        },
        'collapsible-up': {
          from: {
            height: 'var(--radix-collapsible-content-height)',
          },
          to: {
            height: 0,
          },
        },
        shake: {
          '0%, 100%': { transform: 'translateX(0)' },
          '25%': { transform: 'translateX(-5px)' },
          '50%': { transform: 'translateX(5px)' },
          '75%': { transform: 'translateX(-5px)' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        'collapsible-down': 'collapsible-down 0.2s ease-in-out',
        'collapsible-up': 'collapsible-up 0.2s ease-in-out',
        shake: 'shake 0.3s ease-in-out',
      },
    },
  },
  plugins: [animate, containerQueries],
};
