<script setup lang="ts">
import { Check, Edit, PlusIcon, Trash2, X } from 'lucide-vue-next';
import { onMounted, reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import PageLoader from '@/components/global/PageLoader.vue';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogFooter, AlertDialogHeader, AlertDialogTrigger } from '@/shadcn-components/ui/alert-dialog';
import { Button as ShadCnButton } from '@/shadcn-components/ui/button';
import Input from '@/shadcn-components/ui/inputs/Input.vue';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/shadcn-components/ui/table';
import { useAuthStore } from '@/stores/auth-store';
import adminApi from '@/util/adminAxios';
import { cloneObject } from '@/util/objects';
import { deployToast, ToastType } from '@/util/toast';

interface TurnstileCategory {
  id: string;
  name: string;
  zone_level: number;
}

const { t } = useI18n();
const authStore = useAuthStore();

const categories = ref<TurnstileCategory[]>([]);
const formerCategories = ref<TurnstileCategory[]>([]);
const loading = ref(false);

const creatingNew = ref(false);
const editingId = ref<string>();

const newItem = reactive<Partial<Pick<TurnstileCategory, 'name' | 'zone_level'>>>({
  name: '',
  zone_level: undefined,
});

const getCategories = async() => {
  const to = setTimeout(() => {
    loading.value = true;
  }, 220);
  try {
    const { data } = await adminApi.get('/api/admin/turnstiles/categories');
    clearTimeout(to);
    loading.value = false;
    return data.data;
  } catch {
    clearTimeout(to);
    loading.value = false;
    return [];
  }
};

const createNewCategory = async() => {
  try {
    await adminApi.post('/api/admin/turnstiles/categories', newItem);
    categories.value = await getCategories();
    formerCategories.value = cloneObject(categories.value);
    resetNewItem();
    deployToast(ToastType.SUCCESS, {
      text: t('misc.success'),
      timeout: 3000,
    });
  } catch {
    deployToast(ToastType.ERROR, {
      text: t('misc.error'),
      timeout: 6000,
    });
  }
};

const resetNewItem = () => {
  newItem.name = '';
  newItem.zone_level = undefined;
  creatingNew.value = false;
};

const saveEdits = async(category: TurnstileCategory) => {
  try {
    await adminApi.put(`/api/admin/turnstiles/categories/${category.id}`, category);
    editingId.value = undefined;
    deployToast(ToastType.SUCCESS, {
      text: t('misc.success'),
      timeout: 3000,
    });
  } catch {
    deployToast(ToastType.ERROR, {
      text: t('misc.error'),
      timeout: 6000,
    });
  }
};

const removeCategory = async(id: string) => {
  try {
    await adminApi.delete(`/api/admin/turnstiles/categories/${id}`);
    categories.value = await getCategories();
    formerCategories.value = cloneObject(categories.value);
    deployToast(ToastType.SUCCESS, {
      text: t('misc.success'),
      timeout: 3000,
    });
  } catch {
    deployToast(ToastType.ERROR, {
      text: t('misc.error'),
      timeout: 6000,
    });
  }
};

const cancelEdits = (category: TurnstileCategory) => {
  const former = formerCategories.value.find(c => c.id === category.id);
  if (former) {
    category.name = former.name;
    category.zone_level = former.zone_level;
  }
  editingId.value = undefined;
};

onMounted(async() => {
  categories.value = await getCategories();
  formerCategories.value = cloneObject(categories.value);
});
</script>

<template>
  <div class="flex flex-col">
    <div v-if="!loading" class="flex flex-col">
      <div class="flex items-center gap-4 mb-4">
        <ShadCnButton
          :disabled="!authStore.hasPermission('turnstile manage')"
          class="w-fit bg-gray-200 text-gray-600 hover:bg-gray-300 gap-2"
          @click="creatingNew = true"
        >
          <PlusIcon class="w-4 h-4" />
          {{ $t('misc.add-new') }}
        </ShadCnButton>
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{{ $t('turnstiles.name') }}</TableHead>
            <TableHead>{{ $t('turnstiles.zone-level') }}</TableHead>
            <TableHead class="text-right">{{ $t('user-management.actions') }}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <!-- New item row -->
          <TableRow v-if="creatingNew">
            <TableCell>
              <Input
                v-model="newItem.name"
                :placeholder="$t('turnstiles.name')"
                class="w-full"
              />
            </TableCell>
            <TableCell>
              <Input
                v-model.number="newItem.zone_level"
                type="number"
                :placeholder="$t('turnstiles.zone-level')"
                class="w-24"
              />
            </TableCell>
            <TableCell class="text-right">
              <div class="flex gap-2 justify-end">
                <ShadCnButton
                  :disabled="!newItem.name || newItem.zone_level === undefined"
                  size="sm"
                  class="w-8 h-8 p-1.5"
                  @click="createNewCategory"
                >
                  <Check class="w-full h-full" />
                </ShadCnButton>
                <ShadCnButton
                  variant="outline"
                  size="sm"
                  class="w-8 h-8 p-1.5"
                  @click="resetNewItem"
                >
                  <X class="w-full h-full" />
                </ShadCnButton>
              </div>
            </TableCell>
          </TableRow>

          <!-- Existing categories -->
          <TableRow v-for="category in categories" :key="category.id">

            <TableCell>
              <Input
                v-if="editingId === category.id"
                v-model="category.name"
                class="w-full"
              />
              <span v-else>{{ category.name }}</span>
            </TableCell>

            <TableCell>
              <Input
                v-if="editingId === category.id"
                v-model.number="category.zone_level"
                type="number"
                class="w-24"
              />
              <span v-else>{{ category.zone_level }}</span>
            </TableCell>

            <TableCell v-if="editingId !== category.id" class="text-rights w-[110px] ml-auto flex gap-2 justify-end items-center">
              <div class="inline-flex items-center">
                <button class="size-8 p-1.5 rounded-full bg-paynes-gray text-primary-foreground hover:bg-paynes-gray/90" @click="editingId = category.id">
                  <Edit class="w-full h-full" />
                </button>
              </div>

              <AlertDialog>
                <AlertDialogTrigger :disabled="false" @click.prevent.stop>
                  <ShadCnButton class="w-8 h-8 p-1.5 rounded-full" variant="destructive">
                    <Trash2 class="w-full h-full" />
                  </ShadCnButton>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    {{ $t('turnstiles.delete-category-confirmation', { name: category.name }) }}
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>{{ $t('misc.cancel') }}</AlertDialogCancel>
                    <AlertDialogAction class="bg-destructive text-destructive-foreground hover:bg-destructive/90" @click="removeCategory(category.id)">{{ $t('misc.continue') }}</AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </TableCell>
            <TableCell v-else class="text-rights w-[110px] ml-auto flex gap-2 justify-end items-center">
              <ShadCnButton
                size="sm"
                class="w-8 h-8 p-1.5 rounded-full"
                @click="saveEdits(category)"
              >
                <Check class="w-full h-full" />
              </ShadCnButton>
              <ShadCnButton
                variant="outline"
                size="sm"
                class="w-8 h-8 p-1.5 rounded-full"
                @click="cancelEdits(category)"
              >
                <X class="w-full h-full" />
              </ShadCnButton>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>

    <PageLoader v-else :absolute-center="true" />
  </div>
</template>
