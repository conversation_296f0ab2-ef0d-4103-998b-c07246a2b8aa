<script setup lang="ts">
import { Edit, PlusIcon, Trash2 } from 'lucide-vue-next';
import { ref } from 'vue';
import PageLoader from '@/components/global/PageLoader.vue';
import { routeMap } from '@/router/routes';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/shadcn-components/ui/alert-dialog';
import { Button } from '@/shadcn-components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/shadcn-components/ui/table';
import adminApi from '@/util/adminAxios';
import { getFromMultiLangObject } from '@/util/multilang';
import type { Response, ProductFamily } from '@/util/types/api-responses';
import { ComponentStateType } from '@/util/types/components';

const componentState = ref(ComponentStateType.OK);

const families = ref<ProductFamily[]>([]);

const fetchData = async() => {
  componentState.value = ComponentStateType.LOADING;
  try {
    const { data } = await adminApi.get<Response<ProductFamily[]>>('/product-families');
    families.value = data.data;
    componentState.value = ComponentStateType.OK;
  } catch {
    componentState.value = ComponentStateType.ERROR;
  }
};

await fetchData();

const removeProduct = async(id: string) => {
  try {
    await adminApi.delete(`/product-families/${id}`);
    await fetchData();
  } catch {
    componentState.value = ComponentStateType.ERROR;
  }
};
</script>

<template>
  <div class="flex flex-col">
    <div v-if="componentState === ComponentStateType.OK" class="flex flex-col">
      <div class="flex items-center gap-4 mb-2 flex-wrap">
        <router-link :to="{ name: routeMap.products.children.familyCreate.name }" class="w-fit bg-gray-200 rounded-lg p-2 hover:bg-gray-300 transition-colors gap-0.5 flex items-center cursor-pointer">
          <PlusIcon class="aspect-square text-gray-500" />
          <div class="text-sm font-medium text-gray-600">{{ $t('products.new-family') }}</div>
        </router-link>
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{{ $t('products.name') }}</TableHead>
            <TableHead class="text-right">
              {{ $t('user-management.actions') }}
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-for="(family, idx) in families" :key="idx">
            <TableCell v-if="family.name" class="hover:underline">
              <router-link :to="{ name: routeMap.products.children.familyEdit.name, params: { id: family.id } }">
                {{ getFromMultiLangObject(family.name).value }}
              </router-link>
            </TableCell>
            <TableCell v-else>-</TableCell>

            <TableCell class="text-rights w-[110px] ml-auto flex gap-2 justify-end">
              <div class="inline-flex items-center">
                <router-link :to="{ name: routeMap.products.children.familyEdit.name, params: { id: family.id } }" class="size-8 p-1.5 rounded-full bg-paynes-gray text-primary-foreground hover:bg-paynes-gray/90">
                  <Edit class="w-full h-full" />
                </router-link>
              </div>

              <AlertDialog>
                <AlertDialogTrigger @click.prevent.stop>
                  <Button class="w-8 h-8 p-1.5 rounded-full" variant="destructive">
                    <Trash2 class="w-full h-full" />
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>{{ $t('products.delete-family', { name: getFromMultiLangObject(family.name).value }) }}</AlertDialogTitle>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>{{ $t('misc.cancel') }}</AlertDialogCancel>
                    <AlertDialogAction class="bg-destructive text-destructive-foreground hover:bg-destructive/90" @click="removeProduct(family.id)">{{ $t('misc.continue') }}</AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
      <div v-if="families?.length === 0" class="text-sm text-gray-400 w-full text-center mt-4">
        {{ $t('positions.no-positions') }}
      </div>
    </div>
    <div v-if="componentState === ComponentStateType.LOADING" class="absolute bg-white/70 top-0 left-0 w-full h-full rounded-3xl">
      <PageLoader :fixed-center="true" />
    </div>
    <div v-else-if="componentState === ComponentStateType.ERROR" class="absolute-center bg-black text-white font-bold p-1">
      <span>{{ $t('misc.failed-to-get-data') }}!</span>
    </div>
  </div>
</template>
