<script setup lang="ts">
import { ArrowUpRightIcon, RefreshCcwIcon } from 'lucide-vue-next';
import { ref, onMounted, computed, watch } from 'vue';
import type BusinessRecord from '@/models/BusinessRecord';
import adminApi from '@/util/adminAxios';

interface Props {
  businessRecordPostUrl: string,
  businessRecordGetUrl: string,
  usersUrl?: string,
}

const props = defineProps<Props>();

const mentionRef = ref();
const businessRecords = ref<BusinessRecord[]>([]);
const newBusinessRecord = ref('');
const users = ref<{ value: string, label: string }[]>([]);

const getUsers = async() => {
  users.value = (await adminApi.get(props.usersUrl ?? '/users')).data.data?.map((user: any) => ({
    label: user.name,
    value: `user:${user.id}`,
  })) ?? [];
};

const getBusinessRecords = async() => {
  try {
    const response = await adminApi.get<{ data: BusinessRecord[] }>(props.businessRecordGetUrl);
    businessRecords.value = response.data.data;
  } catch {
    // TODO: Toast
  }
};

// Function to save comments to local storage
const uploadBusinessRecord = async(brToSave: string, controller?: AbortController) => {
  try {
    const response = await adminApi.post<{data: BusinessRecord}>(props.businessRecordPostUrl, {
      content: brToSave,
    }, { signal: controller?.signal });
    return response.data.data;
  } catch {
    // TODO: Toast
  }
};

onMounted(() => {
  getBusinessRecords();
  getUsers();
});

const addComment = async() => {
  if (!newBusinessRecord.value.trim()) {
    return;
  }
  const newBusinessRecordEntry = await uploadBusinessRecord(convertLabelsToValues(newBusinessRecord.value));
  if (newBusinessRecordEntry) {
    businessRecords.value.unshift(newBusinessRecordEntry);
    newBusinessRecord.value = '';
  }
};

const formatDate = (date: string) => {
  return new Date(date).toLocaleString();
};

const getInitials = (br: BusinessRecord) => {
  const splitName = br.created_by.name.split(' ');
  if (!splitName) {
    return '??';
  }
  return splitName[0]?.charAt(0) + (splitName[1]?.charAt(0) ?? '');
};

const sortedBusinessRecords = computed<BusinessRecord[]>(() => {
  return [...businessRecords.value].sort((a, b) => (new Date(b.created)).valueOf() - (new Date(a.created)).valueOf()) as BusinessRecord[];
});

const nameify = (content: string) => content.replace(/@user:\d+/g, (match) => {
  const user = users.value.find(u => u.value === match.slice(1));
  return user ? `@${user.label}` : match;
});

function convertLabelsToValues(text: string) {
  let result = text;
  users.value.forEach(user => {
    const labelWithAt = `@${user.label}`;
    const valueWithAt = `@${user.value}`;

    result = result.split(labelWithAt).join(valueWithAt);
  });
  return result;
}

function convertValuesToLabels(text: string) {
  return text.replace(/@user:\d+/g, (match) => {
    const user = users.value.find(u => u.value === match.slice(1));
    return user ? `@${user.label}` : match;
  });
}

function handleMentionSelect() {
  setTimeout(() => {
    const textarea = mentionRef.value?.$el?.querySelector('textarea');
    if (textarea) {
      const len = textarea.value.length;
      textarea.setSelectionRange(len, len);
      textarea.focus();
    }
  });
}

watch(newBusinessRecord, val => {
  const replaced = convertValuesToLabels(val);
  if (val !== replaced) {
    newBusinessRecord.value = replaced;
  }
});
</script>

<template>
  <div class="h-full flex flex-col">
    <div class="pr-2 flex-grow overflow-y-auto mb-4 custom-y-scrollbar-1">
      <TransitionGroup name="comment-list" tag="div">
        <div v-for="br in sortedBusinessRecords" :key="br.id" class="mb-2 px-2.5 py-3 bg-paynes-gray-900 rounded-lg shadow comment-item">
          <div class="flex items-center mb-2">
            <div class="w-10 h-10 rounded-full mr-2 bg-paynes-gray-600 text-center flex items-center justify-center">
              <span class="font-bold text-white">{{ getInitials(br) }}</span>
            </div>
            <div>
              <p class="text-sm font-semibold">{{ br.created_by.name }}, <span class="font-normal">{{ br.created_by.email }}</span></p>
              <p class="text-sm text-gray-600">{{ formatDate(br.created) }}</p>
            </div>
          </div>
          <p v-urlify class="text-gray-800" v-html="nameify(br.content)" />
        </div>
      </TransitionGroup>
    </div>

    <div class="relative min-h-[6.8rem] overflow-hidden br">
      <el-mention
        ref="mentionRef"
        v-model="newBusinessRecord"
        type="textarea"
        :options="users"
        class="h-[6.75rem] resize-none bg-neutral-100 w-full p-1 focus-visible:outline-0 rounded"
        whole
        :placeholder="$t('misc.add-comment')"
        @select="handleMentionSelect"
      />
      <button
        class="absolute bottom-3 right-2 w-9 h-9 rounded-full bg-paynes-gray-500 text-white hover:bg-paynes-gray-600 transition duration-300 grid place-items-center"
        @click="addComment"
      >
        <ArrowUpRightIcon />
      </button>
    </div>

    <div class="mt-2 text-xs text-gray-600 flex items-center justify-between">
      <div>
        {{ $t('misc.total-br') }}: {{ businessRecords.length }}
      </div>
      <button class="cursor-pointer bg-paynes-gray-900 px-1 py-0.5 rounded-sm flex items-center" @click="getBusinessRecords">
        <RefreshCcwIcon class="h-3.5 -ml-1 -mr-0.5" />
        <span>{{ $t('misc.refresh') }}</span>
      </button>
    </div>
  </div>
</template>

<style>
.comment-list-enter-active,
.comment-list-leave-active {
  transition: all 0.5s ease;
}
.comment-list-enter-from,
.comment-list-leave-to {
  opacity: 0;
  transform: scale(0.6);
}
.comment-list-move {
  transition: transform 0.5s ease;
}
.comment-item {
  transition: all 0.5s ease;
}

.br .el-textarea__inner {
  background: #f3f3f3 !important;
  border: none !important;
  box-shadow: none !important;
  resize: none;
  height: 100% !important;
  &::-webkit-scrollbar {
    display: none !important;
  }
}
</style>
