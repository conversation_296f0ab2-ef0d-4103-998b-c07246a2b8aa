<script lang="ts" setup>
import ResetPasswordForm from '@/pages/reset-password/ResetPasswordForm.vue';
import { routeMap } from '@/router/routes';
</script>

<template>
  <div class="w-screen h-[100dvh] flex items-center justify-center">
    <div class="w-full h-full flex justify-center items-center lg:items-stretch lg:grid lg:min-h-[600px] lg:grid-cols-2 xl:min-h-[800px]">
      <router-link :to="{name: routeMap.home.name}" class="hidden bg-muted lg:flex items-center justify-center">
        <div class="h-96 w-auto" />
      </router-link>
      <div class="flex items-center justify-center py-12">
        <ResetPasswordForm />
      </div>
    </div>
  </div>
</template>
