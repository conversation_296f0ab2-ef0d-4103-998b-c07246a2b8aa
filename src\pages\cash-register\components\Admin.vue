<script setup lang="ts">
import { IconReceiptEuro, IconLogout, IconShoppingCartSearch, IconCancel, IconPlus, IconUserDollar, IconList, IconReload, IconTicket } from '@tabler/icons-vue';
import { reactive } from 'vue';
import CancelCheckModal from '@/pages/cash-register/dialogs/CancelCheckModal.vue';
import CustomerSelectionModal from '@/pages/cash-register/dialogs/CustomerSelectionModal.vue';
import DepositWithdrawalModal from '@/pages/cash-register/dialogs/DepositWithdrawalModal.vue';
import InvoicePaymentModal from '@/pages/cash-register/dialogs/InvoicePaymentModal.vue';
import OrdersModal from '@/pages/cash-register/dialogs/OrdersModal.vue';
import RefundModal from '@/pages/cash-register/dialogs/RefundModal.vue';
import ReportModal from '@/pages/cash-register/dialogs/ReportModal.vue';
import SurchargeModal from '@/pages/cash-register/dialogs/SurchargeModal.vue';
import TransactionsModal from '@/pages/cash-register/dialogs/TransactionsModal.vue';
import ZeroReceiptResetModal from '@/pages/cash-register/dialogs/ZeroReceiptResetModal.vue';
import { useCashAuthStore } from '@/stores/cash-auth-store';
import { useCashRegisterStore } from '@/stores/cash-register';
import cashApi from '@/util/cashAxios';
import OperationsModal from '../dialogs/OperationsModal.vue';

const store = useCashRegisterStore();
const authStore = useCashAuthStore();

const modals = reactive({
  operations: false,
  cancelBill: false,
  transactions: false,
  depositWithdrawal: false,
  orders: false,
  report: false,
  refund: false,
  invoicePayment: false,
  zeroReceiptReset: false,
  customerSelection: false,
  surcharge: false,
});

const newOrder = async() => {
  const { data: { data }} = await cashApi.post('/api/cash-register/orders');
  store.order = data;
};

const reload = () => {
  cashApi.post('/api/cash-register/frontends/reload');
};
</script>

<template>
  <div class="bg-white border-b fig p-2.5">
    <button v-if="store.order" class="px-4 border h-full rounded-md border-emerald-200 bg-emerald-50 text-emerald-700" @click="newOrder">
      <div class="flex gap-2">
        <IconPlus />
        <div>{{ $t('cash-register.new-bill') }}</div>
      </div>
    </button>
    <button v-if="store.order" class="px-4 border h-full rounded-md border-gray-200 bg-gray-50 text-gray-700" @click="modals.cancelBill = true">
      <div class="flex gap-2.5">
        <IconCancel />
        <div>{{ $t('cash-register.cancel-bill') }}</div>
      </div>
    </button>
    <button class="px-4 border h-full rounded-md border-blue-200 bg-blue-50 text-blue-700" @click="modals.orders = true">
      <div class="flex gap-2.5">
        <IconShoppingCartSearch />
        <div>{{ $t('cash-register.bills') }}</div>
      </div>
    </button>
    <button class="px-4 border h-full rounded-md border-sky-200 bg-sky-50 text-sky-700" @click="modals.transactions = true">
      <div class="flex gap-2.5">
        <IconList />
        <div>{{ $t('cash-register.transactions') }}</div>
      </div>
    </button>
    <button class="px-4 border h-full rounded-md border-orange-200 bg-orange-50 text-orange-700" @click="modals.operations = true">
      <div class="flex gap-2.5">
        <IconReceiptEuro />
        <div>{{ $t('cash-register.operations') }}</div>
      </div>
    </button>
    <button class="px-4 border h-full rounded-md border-violet-200 bg-violet-50 text-violet-700" @click="modals.customerSelection = true">
      <div class="flex gap-2.5">
        <IconUserDollar />
        <div>{{ $t('cash-register.customer') }}</div>
      </div>
    </button>
    <button class="px-4 border h-full rounded-md border-red-200 bg-red-50 text-red-700" @click="modals.surcharge = true">
      <div class="flex gap-2.5">
        <IconTicket />
        <div>{{ $t('cash-register.surcharge') }}</div>
      </div>
    </button>

    <button class="px-4 border border-slate-200 h-full rounded-md ml-auto bg-slate-50 text-slate-900" @click="reload">
      <div class="flex gap-1.5">
        <IconReload />
      </div>
    </button>
    <button class="px-4 border border-rose-200 h-full rounded-md bg-rose-50 text-rose-900" @click="authStore.logout">
      <div class="flex gap-1.5">
        <IconLogout />
        <div>{{ $t('login.log-out') }}</div>
      </div>
    </button>

    <CancelCheckModal v-if="modals.cancelBill" @close="modals.cancelBill = false" />
    <CustomerSelectionModal v-if="modals.customerSelection" @close="modals.customerSelection = false" />
    <SurchargeModal v-if="modals.surcharge" @close="modals.surcharge = false" />
    <TransactionsModal v-if="modals.transactions" @close="modals.transactions = false" />
    <DepositWithdrawalModal v-if="modals.depositWithdrawal" @close="modals.depositWithdrawal = false" />
    <OrdersModal v-if="modals.orders" @close="modals.orders = false" />
    <RefundModal v-if="modals.refund" @close="modals.refund = false" />
    <InvoicePaymentModal v-if="modals.invoicePayment" @close="modals.invoicePayment = false" />
    <ZeroReceiptResetModal v-if="modals.zeroReceiptReset" @close="modals.zeroReceiptReset = false" />
    <ReportModal v-if="modals.report" @close="modals.report = false" />
    <OperationsModal
      v-if="modals.operations"
      @open-deposit-withdrawal="modals.depositWithdrawal = true"
      @open-report="modals.report = true"
      @open-refund="modals.refund = true"
      @open-invoice-payment="modals.invoicePayment = true"
      @open-zero-receipt-reset="modals.zeroReceiptReset = true"
      @close="modals.operations = false"
    />
  </div>
</template>
