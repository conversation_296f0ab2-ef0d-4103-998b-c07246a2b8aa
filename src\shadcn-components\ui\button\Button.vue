<script setup lang="ts">
import { Primitive, type PrimitiveProps } from 'radix-vue';
import { cn } from '@/shadcn-utils';
import { type ButtonVariants, buttonVariants } from '.';
import type { HTMLAttributes } from 'vue';

interface Props extends PrimitiveProps {
  variant?: ButtonVariants['variant']
  size?: ButtonVariants['size']
  class?: HTMLAttributes['class']
}

const props = withDefaults(defineProps<Props>(), {
  as: 'button',
  class: undefined,
  size: undefined,
  variant: undefined,
});
</script>

<template>
  <Primitive
    :as="as"
    :as-child="asChild"
    :class="cn(buttonVariants({ variant, size }), props.class)"
  >
    <slot />
  </Primitive>
</template>
