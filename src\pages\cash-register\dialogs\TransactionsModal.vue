<script setup lang="ts">
import { ref } from 'vue';
import Pagination from '@/components/global/Pagination.vue';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/shadcn-components/ui/table';
import cashApi from '@/util/cashAxios';
import { formatDate } from '@/util/datetime';
import type { CRTransaction, Response } from '@/util/types/api-responses';

const emit = defineEmits(['close']);

const transactions = ref<Response<CRTransaction[]>>();

const fetchData = async({ page = 1 }: { page?: number } = {}) => {
  const { data } = await cashApi.get<Response<CRTransaction[]>>('api/cash-register/transactions', {
    params: {
      page,
      limit: 5,
    },
  });

  transactions.value = data;
};

const typeBgMap = {
  DEPOSIT: 'bg-green-600',
  WITHDRAWAL: 'bg-orange-400',
  PAYMENT: 'bg-teal-500',
};

const methodBgMap = {
  CASH: 'bg-green-600',
  PAYMENT_CARD: 'bg-blue-500',
  VOUCHER: 'bg-amber-500',
};

fetchData();
</script>

<template>
  <div class="fixed inset-0 z-50 bg-black/65 grid place-items-center" @click.self="emit('close')">
    <div class="max-w-[520px] w-full bg-white p-10 rounded-xl">
      <h2 class="text-xl font-bold">{{ $t('cash-register.transaction-history') }}</h2>

      <Table class="mt-4">
        <TableHeader>
          <TableRow>
            <TableHead>{{ $t('misc.type') }}</TableHead>
            <TableHead>{{ $t('misc.amount') }}</TableHead>
            <TableHead>{{ $t('misc.method') }}</TableHead>
            <TableHead class="text-right">{{ $t('misc.date') }}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-for="transaction in transactions?.data" :key="transaction.id">
            <TableCell>
              <div :class="[typeBgMap[transaction.type], 'text-white w-fit rounded-full px-2.5 uppercase']">
                {{ $t(`cash-register.${transaction.type.toLowerCase()}`) }}
              </div>
            </TableCell>
            <TableCell class="font-medium">{{ transaction.amount }} {{ transaction.currency === 'EUR' ? '€' : transaction.currency }}</TableCell>
            <TableCell>
              <div :class="[methodBgMap[transaction.method], 'text-white w-fit rounded-full px-2.5 uppercase']">
                {{ $t(`cash-register.${transaction.method.toLowerCase()}`) }}
              </div>
            </TableCell>
            <TableCell class="text-right text-gray-400">{{ formatDate(transaction.created_at) }}</TableCell>
          </TableRow>
        </TableBody>
      </Table>

      <Pagination v-if="transactions?.meta" in-cash-register :meta="transactions.meta" @new-page="fetchData" />
    </div>
  </div>
</template>
