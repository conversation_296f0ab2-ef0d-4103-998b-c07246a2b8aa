<script setup lang="ts">
import { IconCreditCard, IconLoader2, IconSearch } from '@tabler/icons-vue';
import debounce from 'lodash-es/debounce';
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { useCashRegisterStore } from '@/stores/cash-register';
import cashApi from '@/util/cashAxios';
import { CustomEvents } from '@/util/facades/custom-event';
import type { Customer, Response } from '@/util/types/api-responses';
import { AndroidActions } from '../androidRequestHandler';

interface CardResponseData {
  cardNumber: string
}

const emit = defineEmits(['close']);

const store = useCashRegisterStore();
const searchInput = ref('');
const customers = ref<Customer[]>([]);
const loading = ref(false);
const cardLoading = ref(false);

const fetchCustomers = async(search?: string, cardNumber?: string) => {
  loading.value = true;
  try {
    const params: Record<string, string> = {};

    if (search && search.length > 2) {
      params['filter[search]'] = search;
    }

    if (cardNumber) {
      params['filter[card_number]'] = cardNumber;
    }

    const { data } = await cashApi.get<Response<Customer[]>>('/api/cash-register/customers', { params });
    customers.value = data.data;
  } catch (error) {
    console.error('Error fetching customers:', error);
    customers.value = [];
  } finally {
    loading.value = false;
  }
};

const onSearchInput = debounce((ev: Event) => {
  const target = ev.target as HTMLInputElement;
  fetchCustomers(target.value);
}, 500);

const createOnRegisterCommandHandler = (resolve: (value?: CardResponseData | PromiseLike<CardResponseData>) => void) => {
  return (e: Event) => {
    const { detail } = e as CustomEvent<{ action: string, data: string }>;

    if (detail.action !== AndroidActions.DATA_FROM_READER) {
      return;
    }

    resolve({ cardNumber: detail.data });
    return;
  };
};

const loadFromCard = async() => {
  cardLoading.value = true;
  searchInput.value = '';
  customers.value = [];

  try {
    await cashApi.post('/api/cash-register/commands', {
      data: {
        action: AndroidActions.DATA_FROM_READER,
      },
    });

    const { promise: cardReadPromise, resolve, reject } = Promise.withResolvers<CardResponseData | undefined>();
    const handler = createOnRegisterCommandHandler(resolve);
    window.eventBus.addEventListener(CustomEvents.DataFromReaderEvent, handler);
    const timeoutId = setTimeout(() => {
      reject('Timeout');
    }, 15000);
    onBeforeUnmount(() => {
      window.eventBus.removeEventListener(CustomEvents.DataFromReaderEvent, handler);
      clearTimeout(timeoutId);
    });

    const cardData = await cardReadPromise;
    clearTimeout(timeoutId);
    window.eventBus.removeEventListener(CustomEvents.DataFromReaderEvent, handler);

    await fetchCustomers(undefined, cardData?.cardNumber);
  } catch (error: any) {
    console.error('Error reading card:', error);
  } finally {
    cardLoading.value = false;
  }
};

const selectCustomer = async(customer: Customer) => {
  try {
    if (!store.order?.id) {
      const { data: { data }} = await cashApi.post('/api/cash-register/orders', { customer_id: customer.id });
      store.order = data;
      await store.loadCart();
      emit('close');
      return;
    }

    await cashApi.put(`/api/cash-register/orders/${store.order.id}`, {
      customer_id: customer.id,
    });

    // Reload the order to get updated customer info
    await store.loadCart();
    emit('close');
  } catch (error) {
    console.error('Error assigning customer to order:', error);
  }
};

onMounted(() => {
  fetchCustomers();
});
</script>

<template>
  <div class="fixed inset-0 z-50 bg-black/65 grid place-items-center" @click.self="emit('close')">
    <div class="max-w-[600px] w-full bg-white p-10 rounded-xl">
      <div class="space-y-4">
        <h2 class="text-xl font-bold">{{ $t('cash-register.select-customer') }}</h2>

        <div class="flex gap-4">
          <div class="relative flex-1 p-2 bg-white border-gray-300 border rounded-lg flex items-center overflow-hidden">
            <div class="pl-0.5 pr-1.5">
              <IconSearch class="min-h-5 h-5 min-w-5 w-5 text-muted-foreground bg-mute" />
            </div>
            <input
              ref="searchInputRef"
              v-model="searchInput"
              type="text"
              :placeholder="$t('misc.search')"
              class="w-full focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-gray-500 outline-none placeholder:text-sm"
              @input="onSearchInput"
            >
          </div>

          <button
            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors flex items-center gap-2"
            :disabled="cardLoading"
            @click="loadFromCard"
          >
            <IconLoader2 v-if="cardLoading" class="animate-spin h-4 w-4" />
            <IconCreditCard v-else class="h-4 w-4" />
            {{ $t('cash-register.load-from-card') }}
          </button>
        </div>

        <div v-if="loading" class="flex justify-center py-8">
          <IconLoader2 class="animate-spin h-8 w-8 text-gray-400" />
        </div>

        <div v-else-if="customers.length" class="max-h-96 overflow-y-auto space-y-2">
          <div
            v-for="customer in customers"
            :key="customer.id"
            class="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
            @click="selectCustomer(customer)"
          >
            <div class="flex justify-between items-start">
              <div class="flex-1">
                <h3 class="font-medium text-gray-900">{{ customer.name }}</h3>
                <div class="text-sm text-gray-500 space-y-1">
                  <div v-if="customer.email">{{ customer.email }}</div>
                  <div v-if="customer.phone">{{ customer.phone }}</div>
                  <div v-if="customer.address">
                    {{ customer.address }}
                    <span v-if="customer.zipcode || customer.city">
                      , {{ customer.zipcode }} {{ customer.city }}
                    </span>
                  </div>
                </div>

                <!-- Customer Groups -->
                <div v-if="customer.customer_groups?.length" class="flex flex-wrap gap-1 mt-2">
                  <span
                    v-for="group in customer.customer_groups"
                    :key="group.id"
                    class="inline-flex px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded"
                  >
                    {{ group.name }}
                  </span>
                </div>

                <!-- Cards -->
                <div v-if="customer.cards?.length" class="flex flex-wrap gap-1 mt-2">
                  <span
                    v-for="card in customer.cards"
                    :key="card.id"
                    class="inline-flex items-center px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded gap-1"
                  >
                    <IconCreditCard class="h-3 w-3" />
                    {{ card.card_number }} ({{ card.card_type }})
                  </span>
                </div>
              </div>

              <!-- Discount Info -->
              <div v-if="customer.discount_percent_calculated > 0" class="text-right">
                <span class="inline-flex px-2 py-1 text-sm bg-green-100 text-green-800 rounded">
                  -{{ customer.discount_percent_calculated }}%
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- No Results -->
        <div v-else class="text-center text-gray-400 italic py-8">
          {{ $t('misc.no-results') }}
        </div>
      </div>
    </div>
  </div>
</template>
