<script setup lang="ts">
import { IconLockFilled, IconCircle, IconCircleFilled } from '@tabler/icons-vue';
import { nextTick, ref } from 'vue';
import Keypad from '@/pages/cash-register/dialogs/components/Keypad.vue';
import { useCashAuthStore } from '@/stores/cash-auth-store';
import cashApi from '@/util/cashAxios';

const authStore = useCashAuthStore();

const emits = defineEmits(['onSuccess', 'close']);

const eidInput = ref();
const disableCashierSwap = ref(false);
const lastUserName = ref(localStorage.getItem('last-username') ?? '');
const badEid = ref(false);
const eid = ref<string[]>(localStorage.getItem('last-eid')?.split('') ?? []);
const pin = ref<(string | null)[]>([null, null, null, null]);
const eidChecked = ref(Boolean(eid.value.length));
const pinDotsRef = ref();
const pinIndex = ref(0);

const addDigitPIN = async(digit: string) => {
  if (pinIndex.value > 3) {
    return;
  }

  pin.value[pinIndex.value++] = digit;

  if (pinIndex.value === 4) {
    try {
      disableCashierSwap.value = true;
      await authStore.login(eid.value.join(''), pin.value.join(''));
      emits('close');
      emits('onSuccess');
      resetForm();
    } catch {
      shakeItOff();
      pinIndex.value = 0;
      pin.value = [null, null, null, null];
    } finally {
      disableCashierSwap.value = false;
    }
  }
};

const deleteDigitPIN = () => {
  if (pinIndex.value === 0) {
    return;
  }

  pin.value[--pinIndex.value] = null;
};

const checkEID = async() => {
  try {
    await cashApi.post('/api/cash-register/auth/check', { eid: eid.value.join('') });
    eidChecked.value = true;
  } catch {
    badEid.value = true;
    setTimeout(() => {
      badEid.value = false;
    }, 3000);
  }
};

const shakeItOff = () => {
  if (pinDotsRef.value) {
    pinDotsRef.value.classList.remove('animate-shake');
    void pinDotsRef.value.offsetWidth;
    pinDotsRef.value.classList.add('animate-shake');
  }
};

const scrollToEnd = () => {
  nextTick(() => {
    const el = eidInput.value;
    if (!el) {
      return;
    }
    el.scrollLeft = el.scrollWidth;
  });
};

const resetForm = (hardReset?: boolean) => {
  if (hardReset) {
    eid.value = [];
    eidChecked.value = false;
  } else {
    eid.value = localStorage.getItem('last-eid')?.split('') ?? [];
    eidChecked.value = Boolean(eid.value);
  }
  pin.value = [null, null, null, null];
  pinIndex.value = 0;
};
</script>

<template>
  <div class="fixed inset-0 z-50 bg-black/65 backdrop-blur grid place-items-center font-medium">
    <div class="max-w-[480px] w-full bg-white p-10 rounded-xl text-xl">

      <div class="mx-auto mt-2 flex flex-col items-center text-lg gap-2">
        <IconLockFilled size="40" />
        <div v-if="eidChecked" class="flex items-center gap-1">
          <div class="text-black/70">{{ $t('cash-register.cashier') }}:</div>
          <div class="font-medium">{{ lastUserName }}</div>
        </div>
      </div>

      <div v-if="eidChecked">
        <div ref="pinDotsRef" class="grid grid-cols-4 text-black mt-6 mb-8">
          <div v-for="(digit, i) in pin" :key="i" class="w-full">
            <IconCircleFilled v-if="digit !== null" class="mx-auto text-black" />
            <IconCircle v-else class="mx-auto text-black/50" />
          </div>
        </div>

        <Keypad class="w-[240px]" @add-digit="addDigitPIN" @delete-digit="deleteDigitPIN" @long-press="pin = [null, null, null, null]; pinIndex = 0;" />
        <button class="w-full bg-black active:bg-black/80 rounded-md h-[50px] text-white font-medium mt-8 disabled:opacity-80" :disabled="disableCashierSwap" @click="resetForm(true)">{{ $t('cash-register.change-the-cashier') }}</button>
      </div>

      <div v-else class="w-full space-y-8">
        <div class="relative">
          <label for="eid">EID:</label>
          <input ref="eidInput" :value="eid.join('')" disabled class="w-full bg-white font-medium text-3xl tracking-widest border-b-2 border-black">
          <div v-if="badEid" class="absolute -bottom-[29px] font-normal text-red-600">
            {{ $t('cash-register.invalidEID') }}
          </div>
        </div>

        <Keypad v-model="eid" class="w-[240px]" @add-digit="scrollToEnd" />
        <button class="w-full bg-black active:bg-black/80 rounded-md h-[50px] text-white font-medium" @click="checkEID">{{ $t('login.signIn') }}</button>
      </div>
    </div>
  </div>
</template>
