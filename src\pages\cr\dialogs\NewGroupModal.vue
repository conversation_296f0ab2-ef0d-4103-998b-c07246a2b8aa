<script setup lang="ts">
import { reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import PageLoader from '@/components/global/PageLoader.vue';
import { Button } from '@/shadcn-components/ui/button';
import Input from '@/shadcn-components/ui/inputs/Input.vue';
import Select from '@/shadcn-components/ui/inputs/Select.vue';
import TreeSelect from '@/shadcn-components/ui/inputs/TreeSelect.vue';
import { Label } from '@/shadcn-components/ui/label';
import adminApi from '@/util/adminAxios';
import { deployToast, ToastType } from '@/util/toast';
import type { CRGroup, Tree } from '@/util/types/api-responses';
import { ComponentStateType } from '@/util/types/components';

interface Props {
  dialogData?: CRGroup
}

const props = defineProps<Props>();

const emit = defineEmits(['onCreate']);

const isOpened = defineModel<boolean>();
const { t } = useI18n();
const componentState = ref(ComponentStateType.OK);

const categoryTree = ref<Tree>([]);
const form = reactive({
  name: props.dialogData?.name ?? '',
  description: props.dialogData?.description ?? '',
  cash_registers: props.dialogData?.cash_registers?.map(cr => cr.id) ?? [],
  categories: props.dialogData?.categories ?? [],
});

const onSubmit = async() => {
  componentState.value = ComponentStateType.LOADING;
  try {
    if (props.dialogData) {
      await adminApi.put(`/api/admin/cash-registers/groups/${props.dialogData.id}`, form);
    } else {
      await adminApi.post('/api/admin/cash-registers/groups', form);
    }

    emit('onCreate');
    deployToast(ToastType.SUCCESS, {
      text: t('misc.success'),
      timeout: 6000,
    });
    isOpened.value = false;
  } catch {
    deployToast(ToastType.ERROR, {
      text: t('misc.error'),
      timeout: 6000,
    });
  }
  componentState.value = ComponentStateType.OK;
};

const { data: { data }} = await adminApi.get('/api/admin/products/categories/tree');
categoryTree.value = data;
</script>

<template>
  <Teleport to="body">
    <el-dialog
      v-model="isOpened"
      width="fit-content"
      :show-close="false"
      header-class="hidden"
      class="!p-0 rounded-2xl"
    >
      <form autocomplete="off" class="relative bg-white p-6 rounded-2xl w-[40rem] max-w-[90vw] overflow-y-auto" @submit.prevent="onSubmit">
        <h3 class="font-bold text-xl mb-2">{{ dialogData ? $t('products.edit-unit') : $t('cash-register.new-group') }}</h3>
        <div class="grid gap-4 py-4">
          <div class="flex items-center gap-4">
            <Label class="text-right w-24">
              {{ $t('misc.title') }}
            </Label>

            <Input v-model="form.name" />
          </div>

          <div class="flex items-center gap-4">
            <Label class="text-right w-24">
              {{ $t('misc.description') }}
            </Label>

            <Input v-model="form.description" />
          </div>

          <div class="flex items-center gap-4">
            <Label class="text-right w-24">
              {{ $t('cash-register.cash-registers') }}
            </Label>

            <Select
              v-model="form.cash_registers"
              multiple
              item-title="name"
              item-value="id"
              autocomplete
              endpoint="api/admin/cash-registers"
            />
          </div>

          <div class="flex items-center gap-4">
            <Label class="text-right w-24">
              {{ $t('products.categories' ) }}
            </Label>

            <TreeSelect
              v-model="form.categories"
              :options="categoryTree"
              multiple
            />
          </div>

          <div class="flex items-center justify-end gap-2">
            <Button type="button" variant="default" class="bg-gray-400/80 hover:bg-gray-400" @click="isOpened = false">
              <span>{{ $t('misc.cancel') }}</span>
            </Button>
            <Button type="submit">
              {{ $t('misc.save') }}
            </Button>
          </div>
          <div v-if="componentState === ComponentStateType.LOADING" class="absolute left-0 top-0 w-full h-full bg-white/70 rounded-2xl">
            <PageLoader :absolute-center="true" />
          </div>
        </div>
      </form>
    </el-dialog>
  </Teleport>
</template>
