import Model from '@/models/Model';

// eslint-disable-next-line @typescript-eslint/no-unsafe-declaration-merging
interface BusinessRecord {
  id: number;
  content: string;
  created: string;
  created_by: {
    id: number;
    name: string;
    email: string;
  };
}

// eslint-disable-next-line @typescript-eslint/no-unsafe-declaration-merging,no-redeclare
class BusinessRecord extends Model {
  // Set the resource route of the model
  resource() {
    return 'business-records';
  }
}

export default BusinessRecord;
