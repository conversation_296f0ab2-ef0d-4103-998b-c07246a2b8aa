<script setup lang="ts">
import { useVModel } from '@vueuse/core';
import { type HTMLAttributes, ref } from 'vue';
import { cn } from '@/shadcn-utils';

const props = defineProps<{
  defaultValue?: string | number
  modelValue?: string | number
  class?: HTMLAttributes['class'],
  required?: boolean,
  placeholder?: string,
}>();

const emits = defineEmits<{(e: 'update:modelValue', payload: string | number): void
}>();

const modelValue = useVModel(props, 'modelValue', emits, {
  passive: true,
  defaultValue: props.defaultValue,
});

const inputEl = ref<HTMLInputElement>();
const inputType = ref('password');

const toggleFieldType = (e: Event) => {
  inputType.value === 'password' ? inputType.value = 'text' : inputType.value = 'password';
  e.stopPropagation();
  e.preventDefault();
};
</script>

<template>
  <div class="relative w-full h-full group">
    <input
      ref="inputEl"
      v-model="modelValue"
      :type="inputType"
      :placeholder
      :required="required"
      :class="cn('flex group-active:ring-ring group-active:border-gray-500 h-10 w-full rounded-md border border-input bg-background pl-3 pr-12 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50', props.class)
      "
    >
    <div class="transition-all absolute -translate-y-1/2 top-1/2 right-[1px] duration-100 hover:bg-gray-100 [&_svg]:hover:text-gray-600 rounded-r-md w-12 h-[calc(100%-2px)] flex items-center justify-center select-none cursor-pointer group" @click="toggleFieldType">
      <font-awesome-icon class="transition-all duration-100 icon-eye text-base text-gray-400 right-[15px]" :class="[inputType === 'password' ? '' : 'right-[0.845rem]']" :icon="inputType === 'password' ? 'eye' : 'eye-slash'" />
    </div>
  </div>
</template>
