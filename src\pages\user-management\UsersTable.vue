<script lang="ts" setup>
import isEqual from 'lodash-es/isEqual';
import { Check, Trash2, X } from 'lucide-vue-next';
import { useI18n } from 'vue-i18n';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/shadcn-components/ui/alert-dialog';
import { Button } from '@/shadcn-components/ui/button';
import Select from '@/shadcn-components/ui/inputs/Select.vue';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/shadcn-components/ui/table';
import adminApi from '@/util/adminAxios';
import { deployToast, ToastType } from '@/util/toast';
import type { PermissionData, RoleData } from '@/util/types/roles-and-permissions';
import type { UserManagementData } from '@/util/types/user-management';

interface Props {
  allPermissions: PermissionData[]
  allRoles: RoleData[],
  formerUserData: UserManagementData
}

const props = defineProps<Props>();
const emit = defineEmits(['userDeleted', 'userUpdated']);

const usersDataChanges = defineModel<UserManagementData>({ required: true });
const { t } = useI18n();

const saveEdits = async(updatedUser: UserManagementData[0]) => {
  try {
    await adminApi.put(`/api/admin/users/${updatedUser.id}`, updatedUser);
    emit('userUpdated');
  } catch {
    deployToast(ToastType.ERROR, {
      text: t('user-management.edit-failed'),
      timeout: 6000,
    });
  }
};

const removeUser = async(userToDelete: UserManagementData[0]) => {
  try {
    await adminApi.delete(`/api/admin/users/${userToDelete.id}`);
    emit('userDeleted');
    deployToast(ToastType.SUCCESS, {
      text: t('user-management.success-delete-user'),
      timeout: 6000,
    });
  } catch {
    deployToast(ToastType.ERROR, {
      text: t('user-management.user-delete-fail'),
      timeout: 6000,
    });
  }
};

const cancelEdits = (user: UserManagementData[0], originalIdx: number) => {
  Object.keys(user)
    .forEach(key => {
      (user[key as keyof UserManagementData[0]] as any) = props.formerUserData[originalIdx][key as keyof UserManagementData[0]];
    });
};
</script>

<template>
  <Table v-if="usersDataChanges.length > 0" class="users-table">
    <TableHeader>
      <TableRow>
        <TableHead>{{ $t('user-management.user') }}</TableHead>
        <TableHead>
          {{ $t('user-management.roles') }}
        </TableHead>
        <TableHead class="text-right">
          {{ $t('user-management.actions') }}
        </TableHead>
      </TableRow>
    </TableHeader>
    <TableBody>
      <TableRow v-for="(user, idx) in [...usersDataChanges]" :key="idx" :class="[isEqual(user, formerUserData[idx]) ? (idx % 2 === 0 ? 'bg-accent' : '') : 'bg-yellow-100 hover:bg-yellow-100']" class="">
        <TableCell class="w-[15rem]">
          <div class="font-medium">
            {{ user.name }}
          </div>
          <div class="inline text-sm text-muted-foreground">
            {{ user.email }}
          </div>
        </TableCell>
        <TableCell>
          <Select
            v-model="user.roles"
            :options="allRoles"
            item-title="name"
            item-value="id"
            multiple
            collapse-tags
            class="!w-fit !min-w-[240px]"
          />
        </TableCell>
        <TableCell class="text-right w-36">
          <div v-if="!isEqual(user, formerUserData[idx])" class="inline-flex items-center gap-1">
            <Button class="w-8 h-8 p-1.5 rounded-full bg-green-500 hover:bg-green-600" variant="default" @click="saveEdits(user)">
              <Check class="w-full h-full" />
            </Button>
            <Button class="w-8 h-8 p-1.5 rounded-full " variant="default" @click="cancelEdits(user, idx)">
              <X class="w-full h-full" />
            </Button>
          </div>
          <AlertDialog v-if="isEqual(user, formerUserData[idx])">
            <AlertDialogTrigger>
              <Button class="w-8 h-8 p-1.5 rounded-full" variant="destructive">
                <Trash2 class="w-full h-full" />
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>{{ $t('user-management.delete-user-action', {name: user.name}) }}</AlertDialogTitle>
                <AlertDialogDescription>{{ $t('user.delete-user.modal-desc') }}</AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>{{ $t('misc.cancel') }}</AlertDialogCancel>
                <AlertDialogAction class="bg-destructive text-destructive-foreground hover:bg-destructive/90" @click="removeUser(user)">{{ $t('misc.continue') }}</AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </TableCell>
      </TableRow>
    </TableBody>
  </Table>
</template>
