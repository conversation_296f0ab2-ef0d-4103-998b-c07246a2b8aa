<script setup lang="ts">
import { computed, type HTMLAttributes } from 'vue';
import { cn } from '@/shadcn-utils';
import { useAuthStore } from '@/stores/auth-store';

const store = useAuthStore();

const emits = defineEmits<{(e: 'update:modelValue', payload: string | number): void}>();
const props = defineProps<{
  defaultValue?: any
  modelValue?: any
  multilang?: boolean
  disabled?: boolean
  errors?: string[]
  height?: string
  placeholder?: string
  class?: HTMLAttributes['class']
}>();

const modelValue = computed({
  get() {
    return props.multilang ? (props.modelValue as Record<string, string>)[store.currentInputLang] || '' : props.modelValue as string;
  },
  set(newVal: string) {
    if (props.multilang) {
      const updated = { ...(props.modelValue as Record<string, string>), [store.currentInputLang]: newVal } as any;
      emits('update:modelValue', updated);
    } else {
      emits('update:modelValue', newVal);
    }
  },
});

const padding = computed(() => {
  if (!props.multilang) {
    return '';
  }
  if (store.currentInputLang === 'zh') {
    return '!pl-14';
  }
  return '!pl-10';
});
</script>

<template>
  <div class="w-full h-full">
    <div class="relative w-full h-full">
      <slot name="left" />
      <button v-if="multilang" class="absolute left-2 top-5 -translate-y-1/2 font-medium text-white bg-paynes-gray-800 px-1 rounded select-none uppercase" @click="store.nextLang">{{ store.currentInputLang === 'zh' ? '中文' : store.currentInputLang }}</button>
      <textarea v-model="modelValue" :placeholder="placeholder" rows="1" :disabled :style="{ 'minHeight': height + 'px' }" :class="cn(padding, 'flex w-full rounded-md border border-input shadow bg-background px-3 py-2 text-sm placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50 focus:outline-paynes-gray-900', props.class, { 'border-rose-500 bg-rose-50/50': errors?.length, 'min-h-24': !height })" />
    </div>
    <div v-for="error in errors" :key="error" class="text-rose-500 text-xs -mt-1.5 ml-1.5">{{ error }}</div>
  </div>
</template>
