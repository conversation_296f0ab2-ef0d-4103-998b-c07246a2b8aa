<script setup lang="ts">
import { reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import PageLoader from '@/components/global/PageLoader.vue';
import { Button } from '@/shadcn-components/ui/button';
import Input from '@/shadcn-components/ui/inputs/Input.vue';
import TreeSelect from '@/shadcn-components/ui/inputs/TreeSelect.vue';
import { Label } from '@/shadcn-components/ui/label';
import adminApi from '@/util/adminAxios';
import { deployToast, ToastType } from '@/util/toast';
import type { Response, Category, Tree, MultiLang } from '@/util/types/api-responses';
import { ComponentStateType } from '@/util/types/components';

interface Props {
  dialogData: { id?: string, label?: MultiLang, parent_id: string | undefined }
  categoryTree: Tree
  isEdit: boolean
}

const props = defineProps<Props>();
const emit = defineEmits(['onCreate']);
const isOpened = defineModel<boolean>();

const { t, locale } = useI18n();
const componentState = ref(ComponentStateType.OK);

const categoryForm = reactive({
  name: { ...props?.dialogData?.label,
  } ?? {
    sk: '',
    en: '',
    zh: '',
  },
  color: '',
  parent_id: props?.dialogData?.parent_id,
});
const errors = ref<Record<string, string[]>>({});

const onSubmit = async() => {
  componentState.value = ComponentStateType.LOADING;
  try {
    if (props.isEdit) {
      await adminApi.put(`api/admin/products/categories/${props.dialogData.id}`, { ...categoryForm, parent_id: categoryForm.parent_id });
    } else {
      console.log(categoryForm.parent_id);
      await adminApi.post('api/admin/products/categories', { ...categoryForm, parent_id: categoryForm.parent_id });
    }

    emit('onCreate');
    deployToast(ToastType.SUCCESS, {
      text: t('misc.success'),
      timeout: 6000,
    });
    isOpened.value = false;
  } catch (e) {
    // @ts-ignore
    errors.value = e?.response?.data?.errors;
    deployToast(ToastType.ERROR, {
      text: t('misc.error'),
      timeout: 6000,
    });
  }
  componentState.value = ComponentStateType.OK;
};

if (props.isEdit) {
  const { data } = await adminApi.get<Response<Category>>(`api/admin/products/categories/${props.dialogData.id}`);
  // @ts-ignore
  categoryForm.parent_id = data.data.parent_id;
}

const treeSelectNormalizer = (node: Tree[number]) => {
  return {
    id: node?.id,
    label: node?.label?.[locale.value],
    children: node?.children,
  };
};
</script>

<template>
  <teleport to="body">
    <el-dialog
      v-model="isOpened"
      width="fit-content"
      :show-close="false"
      header-class="hidden"
      class="!p-0 rounded-2xl"
    >
      <form autocomplete="off" class="relative bg-white p-6 rounded-2xl w-[40rem] max-w-[90vw] overflow-visible" @submit.prevent="onSubmit">
        <h3 class="font-bold text-xl mb-2">{{ isEdit ? $t('products.edit-category') : $t('products.new-category') }}</h3>
        <div class="grid gap-4 py-4">
          <div class="space-y-4">
            <div>
              <Label class="text-right w-24">
                {{ $t('misc.title') }}*
              </Label>
              <div class="grid grid-cols-2 gap-2 w-full">
                <div class="flex items-center w-full">
                  <Input
                    v-model="categoryForm.name.sk"
                    :errors="errors.name"
                    type="text"
                    required
                    class="ring-offset-0 pl-8 w-full focus-visible:ring-offset-0 focus-visible:ring-0 focus-visible:border-gray-500"
                  >
                    <template #left>
                      <div class="absolute left-2 top-1/2 -translate-y-1/2 font-medium text-black/40 select-none">SK</div>
                    </template>
                  </Input>
                </div>
                <div class="flex items-center w-full">
                  <Input
                    v-model="categoryForm.name.en"
                    :errors="errors.name"
                    type="text"
                    required
                    class="ring-offset-0 pl-8 w-full focus-visible:ring-offset-0 focus-visible:ring-0 focus-visible:border-gray-500"
                  >
                    <template #left>
                      <div class="absolute left-2 top-1/2 -translate-y-1/2 font-medium text-black/40 select-none">EN</div>
                    </template>
                  </Input>
                </div>
              </div>
            </div>

            <div class="space-x-2">
              <Label class="text-right w-24">
                {{ $t('orders.color') }}
              </Label>
              <el-color-picker v-model="categoryForm.color" />
            </div>

            <div class="-mb-5 w-full">
              <Label class="text-right w-16 min-w-16 sm:w-32 sm:min-w-32 mt-3">
                {{ $t('products.parent-category') }}
              </Label>
              <TreeSelect
                v-model="categoryForm.parent_id"
                :options="categoryTree"
                :errors="errors.parent_id"
                :multiple="false"
                :normalizer="treeSelectNormalizer"
              />
            </div>
          </div>

          <div class="flex items-center justify-end gap-2">
            <Button type="button" variant="default" class="bg-gray-400/80 hover:bg-gray-400" @click="isOpened = false">
              <span>{{ $t('misc.cancel') }}</span>
            </Button>
            <Button type="submit">
              {{ isEdit ? $t('misc.save') : $t('misc.create') }}
            </Button>
          </div>
          <div v-if="componentState === ComponentStateType.LOADING" class="absolute left-0 top-0 w-full h-full bg-white/70 rounded-2xl">
            <PageLoader :absolute-center="true" />
          </div>
        </div>
      </form>
    </el-dialog>
  </teleport>
</template>
