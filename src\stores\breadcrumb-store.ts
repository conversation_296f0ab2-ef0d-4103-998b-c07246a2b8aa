import { defineStore } from 'pinia';
import { ref } from 'vue';

export type ArmBreadcrumb = {
  routeName?: string;
  title: string;
  routeFullPath?: string;
}

export const useBreadcrumbStore = defineStore('breadcrumb-store', () => {
  const breadcrumbs = ref<ArmBreadcrumb[]>([]);

  const appendBreadcrumb = (newItem: ArmBreadcrumb) => {
    breadcrumbs.value.push(newItem);
  };

  const setBreadcrumb = (newItem: ArmBreadcrumb[]) => {
    breadcrumbs.value = newItem;
  };

  const resetBreadcrumb = () => {
    breadcrumbs.value = [];
  };

  return {
    breadcrumbs,
    appendBreadcrumb,
    setBreadcrumb,
    resetBreadcrumb,
  };
});
