{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "include": [
    "src/**/*.ts",
    "src/**/*.json",
    "src/**/*.tsx",
    "src/**/*.vue",
    "typings/*.d.ts",
    "src/shadcn-components/**/*.vue"
  ],
  "exclude": [
    "node_modules",
  ],
  "compilerOptions": {
    "target": "ESNext",
    "module": "ESNext",
    "composite": true,
    "noImplicitAny": false,
    "strict": true,
    "jsx": "preserve",
    "importHelpers": true,
    "moduleResolution": "node",
    "forceConsistentCasingInFileNames": true,
    "isolatedModules": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "noEmit": true,
    "sourceMap": true,
    "baseUrl": ".",
    "resolveJsonModule": true,
    "types": [
      "node",
    ],
    "typeRoots": [
      "typings",
      "node_modules/@types"
    ],
    "paths": {
      "@/*": [
        "src/*"
      ]
    },
    "lib": [
      "esnext",
      "webworker",
      "dom",
      "dom.iterable",
      "scripthost"
    ]
  },
  "references": [
    {
      "path": "./tsconfig.node.json"
    }
  ],
}
