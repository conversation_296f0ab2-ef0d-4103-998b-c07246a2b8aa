<script lang="ts" setup>
import { CircleUser } from 'lucide-vue-next';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import EnIcon from '@/assets/svg/en-flag.svg';
import SkIcon from '@/assets/svg/sk-flag.svg';
import { routeMap } from '@/router/routes';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from '@/shadcn-components/ui/breadcrumb';
import { Button } from '@/shadcn-components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/shadcn-components/ui/dropdown-menu';
import { SidebarTrigger } from '@/shadcn-components/ui/sidebar';
import { useAuthStore } from '@/stores/auth-store';
import { useBreadcrumbStore } from '@/stores/breadcrumb-store';

const { locale } = useI18n();
const { logout } = useAuthStore();

const breadcrumbStore = useBreadcrumbStore();
const router = useRouter();

const flagMap = {
  sk: SkIcon,
  en: EnIcon,
};

const onLogoutPress = async() => {
  await router.replace({ path: routeMap.home.path });
  logout();
};

const changeLang = (lang: 'sk' | 'en') => {
  localStorage.setItem('appLang', lang);
  locale.value = lang;
};
</script>

<template>
  <header class="sticky top-0 z-30 py-2 flex flex-col gap-2 sm:py-4 border-b !bg-white px-4 sm:h-auto sm:border-0 sm:bg-transparent sm:px-6">
    <div class="flex items-center gap-4">
      <SidebarTrigger />
      <Breadcrumb class="hidden md:flex">
        <BreadcrumbList>
          <template v-for="(breadcrumb, idx) in breadcrumbStore.breadcrumbs" :key="breadcrumb.routeName">
            <BreadcrumbItem>
              <BreadcrumbLink as-child>
                <router-link v-if="breadcrumb.routeFullPath || breadcrumb.routeName" :to="breadcrumb.routeFullPath ? breadcrumb.routeFullPath : { name: breadcrumb.routeName }">{{ $t(breadcrumb.title) }}</router-link>
                <div v-else>{{ $t(breadcrumb.title) }}</div>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator v-if="(idx + 1) < breadcrumbStore.breadcrumbs.length" />
          </template>
        </BreadcrumbList>
      </Breadcrumb>
      <div class="ml-auto mr-0 fig">
        <DropdownMenu>
          <DropdownMenuTrigger class="text-sm hover:underline fig gap-0">
            <component :is="flagMap[locale]" class="scale-75" />
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem v-if="locale !== 'sk'" class="fig gap-1 cursor-pointer" @click="changeLang('sk')">
              <SkIcon class="scale-75" />
              <span>{{ $t('misc.sk') }}</span>
            </DropdownMenuItem>
            <DropdownMenuItem v-if="locale !== 'en'" class="fig gap-1 cursor-pointer" @click="changeLang('en')">
              <EnIcon class="scale-75" />
              <span>{{ $t('misc.en') }}</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        <DropdownMenu :modal="false">
          <DropdownMenuTrigger as-child>
            <Button variant="default" size="icon" class="min-w-10 min-h-10 rounded-full bg-gray-400/80">
              <CircleUser class="h-5 w-5" />
              <span class="sr-only">Toggle user menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>{{ $t('user.my-account') }}</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem class="cursor-pointer">
              <router-link :to="{ name: routeMap.settings.name }">
                {{ $t('misc.settings') }}
              </router-link>
            </DropdownMenuItem>
            <DropdownMenuItem :disabled="true" class="cursor-pointer">Admin panel</DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem class="cursor-pointer bg-red-100 hover:bg-red-200 focus:bg-red-200" @click="onLogoutPress">{{ $t('login.log-out') }}</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
    <Breadcrumb class="flex md:hidden">
      <BreadcrumbList>
        <template v-for="(breadcrumb, idx) in breadcrumbStore.breadcrumbs" :key="breadcrumb.routeName">
          <BreadcrumbItem>
            <BreadcrumbLink as-child>
              <router-link :to="breadcrumb.routeFullPath ? breadcrumb.routeFullPath : { name: breadcrumb.routeName }">{{ $t(breadcrumb.title) }}</router-link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator v-if="(idx + 1) < breadcrumbStore.breadcrumbs.length" />
        </template>
      </BreadcrumbList>
    </Breadcrumb>
  </header>
</template>
