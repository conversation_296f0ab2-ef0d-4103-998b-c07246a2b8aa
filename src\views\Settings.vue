<script setup lang="ts">
import { <PERSON>, Setting<PERSON>, Percent, Ruler } from 'lucide-vue-next';
import { useRoute } from 'vue-router';
import TurnstileIcon from '@/assets/svg/turnstile.svg';
import { routeMap } from '@/router/routes';
import { initializeBasicBreadcrumbBehaviour } from '@/util/facades/breadcrumb';

const route = useRoute();

initializeBasicBreadcrumbBehaviour(routeMap.settings.meta.i18nTitle, routeMap.settings.name, true, route);

</script>

<template>
  <div class="grid md:grid-cols-[17rem_1fr] gap-4 xl:max-w-[80vw] 2xl:max-w-[50vw] mx-auto">
    <div class="flex flex-col gap-4 w-full">
      <section class="w-full bg-white drop-shadow-lg rounded-xl">
        <header class="font-bold px-3 pt-2 pb-1">Personal</header>
        <div class="h-px w-full bg-gray-200" />
        <nav class="w-full">
          <ul class="flex flex-col w-full gap-1 p-1 [&_a]:rounded-lg">
            <li class="w-full">
              <router-link exact-active-class="bg-paynes-gray-900/80" class="fig px-3 py-1 hover:bg-paynes-gray-900" :to="{name: routeMap.settings.children.changePassword.name}">
                <Lock class="h-4 w-4 text-paynes-gray-700" />
                <span class="block font-medium">Zmena hesla</span>
              </router-link>
            </li>
          </ul>
        </nav>
      </section>

      <section class="w-full bg-white drop-shadow-lg rounded-xl">
        <header class="font-bold px-3 pt-2 pb-1">{{ $t('settings.general-settings') }}</header>
        <div class="h-px w-full bg-gray-200" />
        <nav class="w-full">
          <ul class="flex flex-col w-full gap-1 p-1 [&_a]:rounded-lg">
            <li class="w-full">
              <router-link exact-active-class="bg-paynes-gray-900/80" class="fig px-3 py-1 hover:bg-paynes-gray-900" :to="{ name: routeMap.settings.children.turnstileSettings.name }">
                <TurnstileIcon class="h-4 w-4 text-paynes-gray-700" />
                <span class="block font-medium">{{ $t('settings.turnstiles-settings') }}</span>
              </router-link>
            </li>
            <li class="w-full">
              <router-link exact-active-class="bg-paynes-gray-900/80" class="fig px-3 py-1 hover:bg-paynes-gray-900" :to="{ name: routeMap.settings.children.unitsSettings.name }">
                <Ruler class="h-4 w-4 text-paynes-gray-700" />
                <span class="block font-medium">{{ $t('settings.measurement-units') }}</span>
              </router-link>
            </li>
            <li class="w-full">
              <router-link exact-active-class="bg-paynes-gray-900/80" class="fig px-3 py-1 hover:bg-paynes-gray-900" :to="{ name: routeMap.settings.children.vatSettings.name }">
                <Percent class="h-4 w-4 text-paynes-gray-700" />
                <span class="block font-medium">{{ $t('settings.taxesTitle') }}</span>
              </router-link>
            </li>
          </ul>
        </nav>
      </section>
    </div>
    <div
      class="bg-white rounded-xl"
      :class="[route.name === routeMap.settings.name ? 'h-fit w-full' : 'w-full flex justify-center md:block md:w-fit max-h-[85dvh] overflow-y-auto overflow-x-hidden drop-shadow-lg' ]"
    >
      <suspense>
        <router-view :key="$route.fullPath" />
      </suspense>
      <div v-if="route.name === routeMap.settings.name" class="py-4 w-full h-full flex flex-col justify-center items-center text-center text-gray-300">
        <Settings class="h-20 w-20" />
        <div class="font-bold">{{ $t('misc.choose-from-menu') }}</div>
      </div>
    </div>
  </div>
</template>
