stages:
  - install
  - build
  - deploy

image: node:lts-alpine

.pnpm:
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - .pnpm-store
      - node_modules/
  before_script:
    - apk update && apk add curl git
    - npm i -g corepack@latest
    - corepack enable
    - corepack prepare pnpm@latest-9 --activate
    - pnpm config set store-dir .pnpm-store
    - echo "//git.antik.sk/api/v4/projects/728/packages/npm/:_authToken=${ANTIK_VITE_GITLAB_TOKEN}" > .npmrc
    - echo "@antik-vite:registry=https://git.antik.sk/api/v4/groups/818/-/packages/npm/" >> .npmrc

.install:
  stage: install
  extends:
    - .pnpm
  script:
    - pnpm i --frozen-lockfile
    - pnpm add envsub
  artifacts:
    expire_in: 1 day
    paths:
      - node_modules/

.deploy:
  image: alpine:latest
  before_script:
    - apk update && apk add openssh-client bash rsync
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add - > /dev/null
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan ${DEPLOY_SERVER} >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    - rsync -crtvz --delete dist/ ${DEPLOY_USER}@${DEPLOY_SERVER}:${DEPLOY_PATH}/public

# Dev
install dev:
  extends:
    - .install
  only:
    - dev

version dev:
  image: alpine:latest
  stage: build
  before_script:
    - apk update && apk add jq
  script:
    - jq --arg number "${CI_COMMIT_REF_NAME}" --arg build "${CI_COMMIT_SHA}" '. + {number:$number,build:$build}' -c version.stub > version.json
  only:
    - dev
  artifacts:
    paths:
      - version.json

build dev:
  stage: build
  environment:
    name: dev
  needs:
    - job: install dev
      artifacts: true
  extends:
    - .pnpm
  script:
    - ./node_modules/.bin/envsub
      --env API_URL=$API_URL
      --env OAUTH_CLIENT_ID=$OAUTH_CLIENT_ID
      --env OAUTH_CLIENT_SECRET=$OAUTH_CLIENT_SECRET
      .env.ci .env
    - pnpm build
  only:
    - dev
  artifacts:
    expire_in: 1 day
    paths:
      - dist

deploy dev:
  stage: deploy
  environment:
    name: dev
  needs:
    - job: version dev
      artifacts: true
    - job: build dev
      artifacts: true
  variables:
    DEPLOY_USER: "gitlab"
    DEPLOY_SERVER: "dev4.antik.sk"
    DEPLOY_PATH: "/home/<USER>/web/antik-smartcityportal-turnstile-app-ui"
  extends:
    - .deploy
  only:
    - dev

# Production
install prod:
  extends:
    - .install
  only:
    - tags

version prod:
  image: alpine:latest
  stage: build
  before_script:
    - apk update && apk add jq
  script:
    - jq --arg number "${CI_COMMIT_REF_NAME}" --arg build "${CI_COMMIT_SHA}" '. + {number:$number,build:$build}' -c version.stub > version.json
  rules:
    - if: $CI_COMMIT_TAG && $CI_COMMIT_REF_NAME =~ /^\d+\.\d+(?:\.\d+)*$/
  artifacts:
    paths:
      - version.json

build prod:
  stage: build
  environment:
    name: production
  needs:
    - job: install prod
      artifacts: true
    - job: version prod
      artifacts: true
  extends:
    - .pnpm
  script:
    - ./node_modules/.bin/envsub
      --env API_URL=$API_URL
      --env OAUTH_CLIENT_ID=$OAUTH_CLIENT_ID
      --env OAUTH_CLIENT_SECRET=$OAUTH_CLIENT_SECRET
      .env.ci .env
    - pnpm build
  rules:
    - if: $CI_COMMIT_TAG && $CI_COMMIT_REF_NAME =~ /^\d+\.\d+(?:\.\d+)*$/
  artifacts:
    expire_in: 14 days
    paths:
      - dist
      - version.json

docker image:
  resource_group: production
  stage: build
  image: docker:stable
  services:
    - name: docker:dind
      command: [ "--tls=false" ]
  environment:
    name: production
  needs:
    - job: build prod
      artifacts: true
  variables:
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: ""
  before_script:
    - echo "$CI_REGISTRY_PASSWORD" | docker login --username $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
    - echo "//git.antik.sk/api/v4/projects/728/packages/npm/:_authToken=${ANTIK_VITE_GITLAB_TOKEN}" > .npmrc
    - echo "@antik-vite:registry=https://git.antik.sk/api/v4/groups/818/-/packages/npm/" >> .npmrc
  script:
    - docker pull $CI_REGISTRY_IMAGE:latest || true
    - >
      docker build
      -t $CI_REGISTRY_IMAGE
      --file ops/image/Dockerfile
      --build-arg CACHE_DATE=$(date +%s)
      --pull
      --cache-from $CI_REGISTRY_IMAGE:latest
      --label "sk.antik.smartcityportal.turnstile.app.ui.title=$CI_PROJECT_TITLE"
      --label "sk.antik.smartcityportal.turnstile.app.ui.url=$CI_PROJECT_URL"
      --label "sk.antik.smartcityportal.turnstile.app.ui.created=$CI_JOB_STARTED_AT"
      --label "sk.antik.smartcityportal.turnstile.app.ui.revision=$CI_COMMIT_SHA"
      --label "sk.antik.smartcityportal.turnstile.app.ui.version=$CI_COMMIT_REF_NAME"
      --tag $CI_REGISTRY_IMAGE:$CI_COMMIT_TAG
      .
    - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_TAG
    - docker push $CI_REGISTRY_IMAGE:latest
  rules:
    - if: $CI_COMMIT_TAG && $CI_COMMIT_REF_NAME =~ /^\d+\.\d+(?:\.\d+)*$/
