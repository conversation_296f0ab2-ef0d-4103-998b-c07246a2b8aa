$trans-cubic-bezier: cubic-bezier(0.215, 0.61, 0.355, 1);
@mixin timing-function {
  animation-timing-function: $trans-cubic-bezier;
}

/* ----------------------------------------------
 * Modified version from Animista
 * Animista is Licensed under FreeBSD License.
 * See http://animista.net/license for more info.
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

@keyframes fadeOutTop {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@keyframes fadeOutLeft {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@keyframes fadeOutBottom {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@keyframes fadeOutRight {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@keyframes fadeInLeft {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes fadeInRight {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes fadeInTop {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes fadeInBottom {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.#{$vt-namespace}__custom-enter-active {
  &.top-left,
  &.bottom-left {
    animation-name: fadeInLeft;
  }
  &.top-right,
  &.bottom-right {
    animation-name: fadeInRight;
  }
  &.top-center {
    animation-name: fadeInTop;
  }
  &.bottom-center {
    animation-name: fadeInBottom;
  }
}

.#{$vt-namespace}__custom-leave-active:not(.disable-transition) {
  &.top-left,
  &.bottom-left {
    animation-name: fadeOutLeft;
  }
  &.top-right,
  &.bottom-right {
    animation-name: fadeOutRight;
  }
  &.top-center {
    animation-name: fadeOutTop;
  }
  &.bottom-center {
    animation-name: fadeOutBottom;
  }
}

.#{$vt-namespace}__custom-leave-active,
.#{$vt-namespace}__custom-enter-active {
  animation-duration: 750ms;
  animation-fill-mode: both;
}

.#{$vt-namespace}__custom-move {
  transition-timing-function: ease-in-out;
  transition-property: all;
  transition-duration: 400ms;
}
