<script setup lang="ts">
import { IconEdit } from '@tabler/icons-vue';
import { useRouteQuery } from '@vueuse/router';
import debounce from 'lodash-es/debounce';
import { Trash2, Search, PlusIcon } from 'lucide-vue-next';
import { reactive, ref } from 'vue';
import PageLoader from '@/components/global/PageLoader.vue';
import Pagination from '@/components/global/Pagination.vue';
import NewGroupModal from '@/pages/cr/dialogs/NewGroupModal.vue';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/shadcn-components/ui/alert-dialog';
import { Button as ShadCnButton } from '@/shadcn-components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/shadcn-components/ui/table';
import adminApi from '@/util/adminAxios';
import { getFromMultiLangObject } from '@/util/multilang';
import type { CRGroup, Meta, Response } from '@/util/types/api-responses';
import { ComponentStateType } from '@/util/types/components';

const componentState = ref(ComponentStateType.LOADING);

const dialogData = reactive<{ modalShown: boolean; data: undefined | CRGroup }>({
  modalShown: false,
  data: undefined,
});
const groups = ref<CRGroup[]>([]);
const paginationMeta = ref<Meta>();
const currentPage = useRouteQuery('page');
const currentLimit = useRouteQuery('limit');

const fetchData = async(args?: { page?: number, limit?: number, search?: string }) => {
  const to = setTimeout(() => {
    componentState.value = ComponentStateType.LOADING;
  }, 220);
  try {
    const params = {
      page: (args?.page ?? currentPage.value) ?? 1,
      limit: (args?.limit ?? currentLimit.value) ?? 15,
      includes: 'categories,cashRegisters',
    } as Record<string, string | number>;
    if (args?.search && args.search.length > 2) {
      params.search = args.search;
      delete params.page;
    }

    const { data } = await adminApi.get<Response<CRGroup[]>>('/api/admin/cash-registers/groups', { params });

    groups.value = data.data;
    paginationMeta.value = data.meta;
    clearTimeout(to);
    componentState.value = ComponentStateType.OK;
  } catch {
    clearTimeout(to);
    componentState.value = ComponentStateType.ERROR;
  }
};

await fetchData();

const newGroup = () => {
  dialogData.modalShown = true;
  dialogData.data = undefined;
};

const editGroup = (group: CRGroup) => {
  dialogData.modalShown = true;
  dialogData.data = group;
};

const removeProduct = async(id: string) => {
  try {
    await adminApi.delete(`/api/admin/cash-registers/groups/${id}`);
    await fetchData();
  } catch {
    componentState.value = ComponentStateType.ERROR;
  }
};

const onSearchInput = debounce((ev: Record<any, any>) => {
  fetchData({ search: ev.target?.value });
}, 500);
</script>

<template>
  <div class="flex flex-col">
    <div v-if="componentState === ComponentStateType.OK" class="flex flex-col">
      <div class="flex items-center gap-4 mb-2 flex-wrap">
        <button class="w-fit bg-gray-200 rounded-lg p-2 hover:bg-gray-300 transition-colors gap-0.5 flex items-center cursor-pointer" @click="newGroup">
          <PlusIcon class="aspect-square text-gray-500" />
          <span class="text-sm font-medium text-gray-600">{{ $t('cash-register.new-group') }}</span>
        </button>

        <div class="relative p-2 bg-white border-gray-300 border rounded-lg flex items-center w-64 overflow-hidden flex-1 sm:flex-none">
          <div class="pl-0.5 pr-1.5">
            <Search class="min-h-5 h-5 min-w-5 w-5 text-muted-foreground bg-mute" />
          </div>
          <input
            type="text"
            :placeholder="$t('misc.search')"
            class="w-full focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-gray-500 outline-none placeholder:text-sm"
            @input="onSearchInput"
          >
        </div>
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{{ $t('products.name') }}</TableHead>
            <TableHead class="text-right">
              {{ $t('user-management.actions') }}
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-for="(group, idx) in groups" :key="idx">
            <TableCell v-if="group.name">
              {{ getFromMultiLangObject(group.name).value }}
            </TableCell>
            <TableCell v-else>-</TableCell>

            <TableCell class="text-rights w-[110px] ml-auto flex gap-2 justify-end">
              <div class="inline-flex items-center">
                <button class="size-8 p-1.5 rounded-full bg-paynes-gray-600 text-primary-foreground hover:bg-paynes-gray-600/90" @click="editGroup(group)">
                  <IconEdit class="w-full h-full" />
                </button>
              </div>

              <AlertDialog>
                <AlertDialogTrigger @click.prevent.stop>
                  <ShadCnButton class="w-8 h-8 p-1.5 rounded-full" variant="destructive">
                    <Trash2 class="w-full h-full" />
                  </ShadCnButton>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>{{ $t('form.delete-cr', { name: group.name }) }}</AlertDialogTitle>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>{{ $t('misc.cancel') }}</AlertDialogCancel>
                    <AlertDialogAction class="bg-destructive text-destructive-foreground hover:bg-destructive/90" @click="removeProduct(group.id)">{{ $t('misc.continue') }}</AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>

      <Pagination v-if="groups?.length" :meta="paginationMeta!" @new-page="fetchData" />

      <div v-if="groups?.length === 0" class="text-sm text-gray-400 w-full text-center mt-4">
        {{ $t('misc.list-is-empty') }}
      </div>
    </div>
    <div v-if="componentState === ComponentStateType.LOADING" class="absolute bg-white/70 top-0 left-0 w-full h-full rounded-3xl">
      <PageLoader :fixed-center="true" />
    </div>
    <div v-else-if="componentState === ComponentStateType.ERROR" class="absolute-center bg-black text-white font-bold p-1">
      <span>{{ $t('misc.failed-to-get-data') }}!</span>
    </div>
  </div>

  <suspense v-if="dialogData.modalShown">
    <NewGroupModal v-model="dialogData.modalShown" :dialog-data="dialogData.data" @on-create="fetchData" />
  </suspense>
</template>
