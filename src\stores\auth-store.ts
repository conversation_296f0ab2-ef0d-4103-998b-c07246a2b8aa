import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import adminApi from '@/util/adminAxios';
import type { OAuthTokenResponse, OAuthUserResponse } from '@/util/types/api-responses';

export enum LocalStorageTokens {
  ACCESS = 'amr-at',
  REFRESH = 'amr-rt',
}

export const TokenService = {
  getAccessToken: () => localStorage.getItem(LocalStorageTokens.ACCESS),
  getRefreshToken: () => localStorage.getItem(LocalStorageTokens.REFRESH),
  setAccessToken: (token: string) => localStorage.setItem(LocalStorageTokens.ACCESS, token),
  setRefreshToken: (token: string) => localStorage.setItem(LocalStorageTokens.REFRESH, token),
  removeTokens: () => {
    localStorage.removeItem(LocalStorageTokens.ACCESS);
    localStorage.removeItem(LocalStorageTokens.REFRESH);
  },
};

export type UserData = {
  id: number,
  name: string,
  email: string,
  permissions: string[],
  roles: {
    id: number,
    name: string,
  }[],
}

const supportedLocales = ['sk', 'en'];
const storedLang = localStorage.getItem('appLang');
const browserLang = navigator.languages?.[0]?.slice(0, 2);

export const useAuthStore = defineStore('auth-store', () => {
  const accessToken = ref(TokenService.getAccessToken() ?? undefined);
  const refreshToken = ref(TokenService.getRefreshToken() ?? undefined);
  const user = ref<UserData>();
  const currentInputLang = ref(storedLang ?? (supportedLocales.includes(browserLang) ? browserLang : 'sk'));

  const nextLang = () => {
    const langs = ['sk', 'en'];
    currentInputLang.value = langs[(langs.indexOf(currentInputLang.value) + 1) % langs.length];
  };

  const fetchUser = async(): Promise<UserData|undefined> => {
    try {
      const { data: userResponseData } = await adminApi.get<OAuthUserResponse>('/api/admin/user');
      user.value = userResponseData.data;
      return userResponseData.data;
    } catch (err) {
      logout();
      return;
    }
  };

  const login = async(username: string, password: string) => {
    try {
      const { data: responseData } = await adminApi.post<OAuthTokenResponse>('/oauth/token', {
        grant_type: 'password',
        client_id: import.meta.env.VITE_APP_CLIENT_ID,
        client_secret: import.meta.env.VITE_APP_CLIENT_SECRET,
        username,
        password,
        scope: '',
      });

      accessToken.value = responseData.access_token;
      TokenService.setAccessToken(responseData.access_token);
      refreshToken.value = responseData.refresh_token;
      TokenService.setRefreshToken(responseData.refresh_token);
      await fetchUser();
    } catch (err) {
      throw new Error('Failed to login');
    }
  };

  const hasPermission = computed(() => (permission: string) => {
    return user.value?.permissions.includes(permission) ?? false;
  });

  const hasAnyPermission = computed(() => (...permissions: string[]) => {
    return permissions.some(permission => user.value?.permissions.includes(permission));
  });

  const promptRefreshToken = async() => {
    try {
      const { data: responseData } = await adminApi.post<OAuthTokenResponse>('/oauth/token', {
        grant_type: 'refresh_token',
        refresh_token: refreshToken.value,
        client_id: import.meta.env.VITE_APP_CLIENT_ID,
        client_secret: import.meta.env.VITE_APP_CLIENT_SECRET,
        scope: '',
      });

      accessToken.value = responseData.access_token;
      TokenService.setAccessToken(responseData.access_token);
      refreshToken.value = responseData.refresh_token;
      TokenService.setRefreshToken(responseData.refresh_token);

      return {
        accessToken: responseData.access_token,
        refreshToken: responseData.refresh_token,
      };
    } catch (err) {
      throw new Error('Failed to get token');
    }
  };

  const logout = () => {
    TokenService.removeTokens();
    accessToken.value = undefined;
    refreshToken.value = undefined;
    user.value = undefined;
  };

  return {
    accessToken,
    refreshToken,
    user,
    hasPermission,
    hasAnyPermission,
    currentInputLang,
    login,
    promptRefreshToken,
    logout,
    fetchUser,
    nextLang,
  };
});
