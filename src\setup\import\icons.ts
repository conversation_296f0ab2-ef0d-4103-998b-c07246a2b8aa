import { library } from '@fortawesome/fontawesome-svg-core';
import { faHeart as farHeart, faClipboard } from '@fortawesome/free-regular-svg-icons';
import { faHome, faEnvelope, faPhoneAlt, faSearch, faChevronUp, faChevronDown, faEyeSlash, faEye, faExclamationTriangle, faRedo, faXmark, faHeart, faCheck, faCircleUser, faGear, faBan, faShareNodes, faCircleInfo } from '@fortawesome/free-solid-svg-icons';

export default (() => {
  const addIconsToLibrary = (): void => {
    [
      faHome,
      faPhoneAlt,
      faEnvelope,
      faSearch,
      faChevronDown,
      faChevronUp,
      faEyeSlash,
      faEye,
      faExclamationTriangle,
      faRedo,
      faXmark,
      faCheck,
      faCircleUser,
      faGear,
      faHeart,
      faBan,
      farHeart,
      faShareNodes,
      faClipboard,
      faCircleInfo,
    ].forEach(icon => {
      library.add(icon);
    });
  };

  return {
    addIconsToLibrary,
  };
})();
