<script setup lang="ts">
import { AlertDialogAction, type AlertDialogActionProps } from 'radix-vue';
import { type HTMLAttributes, computed } from 'vue';
import { buttonVariants } from '@/shadcn-components/ui/button';
import { cn } from '@/shadcn-utils';

const props = defineProps<AlertDialogActionProps & { class?: HTMLAttributes['class'] }>();

const delegatedProps = computed(() => {
  const { ...delegated } = props;

  return delegated;
});
</script>

<template>
  <AlertDialogAction v-bind="delegatedProps" :class="cn(buttonVariants(), props.class)">
    <slot />
  </AlertDialogAction>
</template>
